<RCC>
    <qresource prefix="/">
        <!-- Main QML files -->
        <file>qml/main.qml</file>
        <file>qml/BackupPage.qml</file>
        <file>qml/RestorePage.qml</file>
        <file>qml/SchedulePage.qml</file>
        <file>qml/HistoryPage.qml</file>
        <file>qml/SettingsPage.qml</file>
        
        <!-- Components -->
        <file>qml/components/NavButton.qml</file>
        <file>qml/components/DriveSelector.qml</file>
        <file>qml/components/ProgressIndicator.qml</file>
        <file>qml/components/BackupItemDelegate.qml</file>
        
        <!-- Icons -->
        <file>icons/app-icon.png</file>
        <file>icons/backup.png</file>
        <file>icons/restore.png</file>
        <file>icons/schedule.png</file>
        <file>icons/history.png</file>
        <file>icons/settings.png</file>
        <file>icons/folder.png</file>
        <file>icons/drive.png</file>
        <file>icons/usb-drive.png</file>
        <file>icons/hard-drive.png</file>
        <file>icons/check.png</file>
        <file>icons/arrow-down.png</file>
        <file>icons/backup-quick.png</file>
        <file>icons/play.png</file>
        <file>icons/pause.png</file>
        <file>icons/stop.png</file>
        <file>icons/refresh.png</file>
        <file>icons/delete.png</file>
        <file>icons/edit.png</file>
        <file>icons/calendar.png</file>
        <file>icons/clock.png</file>
        <file>icons/success.png</file>
        <file>icons/warning.png</file>
        <file>icons/error.png</file>
        <file>icons/info.png</file>
        
        <!-- Themes -->
        <file>themes/dark-theme.json</file>
        <file>themes/light-theme.json</file>
    </qresource>
</RCC>
