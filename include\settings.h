#ifndef SETTINGS_H
#define SETTINGS_H

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QStringList>
#include <QJsonObject>
#include <QJsonDocument>
#include <QStandardPaths>

/**
 * @brief Application theme enumeration
 */
enum class AppTheme {
    Light,
    Dark,
    System
};

/**
 * @brief Backup compression level
 */
enum class CompressionLevel {
    None,
    Fast,
    Normal,
    Maximum
};

/**
 * @brief The Settings class manages application configuration
 * 
 * This class provides centralized settings management for the application,
 * including user preferences, backup configurations, UI settings, and
 * application state persistence.
 */
class Settings : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString defaultBackupPath READ defaultBackupPath WRITE setDefaultBackupPath NOTIFY defaultBackupPathChanged)
    Q_PROPERTY(bool autoBackupEnabled READ autoBackupEnabled WRITE setAutoBackupEnabled NOTIFY autoBackupEnabledChanged)
    Q_PROPERTY(int autoBackupInterval READ autoBackupInterval WRITE setAutoBackupInterval NOTIFY autoBackupIntervalChanged)
    Q_PROPERTY(AppTheme theme READ theme WRITE setTheme NOTIFY themeChanged)
    Q_PROPERTY(bool encryptionEnabled READ encryptionEnabled WRITE setEncryptionEnabled NOTIFY encryptionEnabledChanged)
    Q_PROPERTY(CompressionLevel compressionLevel READ compressionLevel WRITE setCompressionLevel NOTIFY compressionLevelChanged)

public:
    explicit Settings(QObject *parent = nullptr);
    ~Settings();

    /**
     * @brief Initialize settings with default values
     */
    void initialize();

    /**
     * @brief Save all settings to persistent storage
     */
    void save();

    /**
     * @brief Load settings from persistent storage
     */
    void load();

    /**
     * @brief Reset all settings to defaults
     */
    void resetToDefaults();

    // General Settings
    QString defaultBackupPath() const { return m_defaultBackupPath; }
    void setDefaultBackupPath(const QString &path);

    bool autoBackupEnabled() const { return m_autoBackupEnabled; }
    void setAutoBackupEnabled(bool enabled);

    int autoBackupInterval() const { return m_autoBackupInterval; }
    void setAutoBackupInterval(int hours);

    QStringList recentBackupPaths() const { return m_recentBackupPaths; }
    void addRecentBackupPath(const QString &path);

    // UI Settings
    AppTheme theme() const { return m_theme; }
    void setTheme(AppTheme theme);

    QString language() const { return m_language; }
    void setLanguage(const QString &language);

    bool showNotifications() const { return m_showNotifications; }
    void setShowNotifications(bool show);

    bool minimizeToTray() const { return m_minimizeToTray; }
    void setMinimizeToTray(bool minimize);

    // Backup Settings
    bool encryptionEnabled() const { return m_encryptionEnabled; }
    void setEncryptionEnabled(bool enabled);

    QString encryptionPassword() const { return m_encryptionPassword; }
    void setEncryptionPassword(const QString &password);

    CompressionLevel compressionLevel() const { return m_compressionLevel; }
    void setCompressionLevel(CompressionLevel level);

    bool incrementalBackupEnabled() const { return m_incrementalBackupEnabled; }
    void setIncrementalBackupEnabled(bool enabled);

    bool verifyBackupIntegrity() const { return m_verifyBackupIntegrity; }
    void setVerifyBackupIntegrity(bool verify);

    QStringList excludePatterns() const { return m_excludePatterns; }
    void setExcludePatterns(const QStringList &patterns);
    void addExcludePattern(const QString &pattern);
    void removeExcludePattern(const QString &pattern);

    // Advanced Settings
    int maxBackupHistory() const { return m_maxBackupHistory; }
    void setMaxBackupHistory(int count);

    bool deleteOldBackups() const { return m_deleteOldBackups; }
    void setDeleteOldBackups(bool deleteOld);

    int networkTimeout() const { return m_networkTimeout; }
    void setNetworkTimeout(int seconds);

    bool enableLogging() const { return m_enableLogging; }
    void setEnableLogging(bool enabled);

    QString logLevel() const { return m_logLevel; }
    void setLogLevel(const QString &level);

    // Window State
    QByteArray windowGeometry() const { return m_windowGeometry; }
    void setWindowGeometry(const QByteArray &geometry);

    QByteArray windowState() const { return m_windowState; }
    void setWindowState(const QByteArray &state);

public slots:
    /**
     * @brief Get setting value by key
     * @param key Setting key
     * @param defaultValue Default value if key doesn't exist
     * @return Setting value
     */
    Q_INVOKABLE QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant());

    /**
     * @brief Set setting value by key
     * @param key Setting key
     * @param value Setting value
     */
    Q_INVOKABLE void setValue(const QString &key, const QVariant &value);

    /**
     * @brief Check if setting key exists
     * @param key Setting key
     * @return true if key exists
     */
    Q_INVOKABLE bool contains(const QString &key);

    /**
     * @brief Remove setting by key
     * @param key Setting key
     */
    Q_INVOKABLE void remove(const QString &key);

    /**
     * @brief Get all setting keys
     * @return List of all setting keys
     */
    Q_INVOKABLE QStringList allKeys();

    /**
     * @brief Export settings to JSON
     * @return JSON string of all settings
     */
    Q_INVOKABLE QString exportSettings();

    /**
     * @brief Import settings from JSON
     * @param jsonString JSON string containing settings
     * @return true if import successful
     */
    Q_INVOKABLE bool importSettings(const QString &jsonString);

signals:
    void defaultBackupPathChanged();
    void autoBackupEnabledChanged();
    void autoBackupIntervalChanged();
    void themeChanged();
    void encryptionEnabledChanged();
    void compressionLevelChanged();
    void settingsChanged(const QString &key);

private:
    void setDefaultValues();
    QString getSettingsFilePath() const;

    QSettings *m_settings;

    // General Settings
    QString m_defaultBackupPath;
    bool m_autoBackupEnabled;
    int m_autoBackupInterval;
    QStringList m_recentBackupPaths;

    // UI Settings
    AppTheme m_theme;
    QString m_language;
    bool m_showNotifications;
    bool m_minimizeToTray;

    // Backup Settings
    bool m_encryptionEnabled;
    QString m_encryptionPassword;
    CompressionLevel m_compressionLevel;
    bool m_incrementalBackupEnabled;
    bool m_verifyBackupIntegrity;
    QStringList m_excludePatterns;

    // Advanced Settings
    int m_maxBackupHistory;
    bool m_deleteOldBackups;
    int m_networkTimeout;
    bool m_enableLogging;
    QString m_logLevel;

    // Window State
    QByteArray m_windowGeometry;
    QByteArray m_windowState;
};

Q_DECLARE_METATYPE(AppTheme)
Q_DECLARE_METATYPE(CompressionLevel)

#endif // SETTINGS_H
