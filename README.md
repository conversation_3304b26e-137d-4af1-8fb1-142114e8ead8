# BackupPro

A professional backup solution built with Qt C++ and QML, providing automated backup, restore, and scheduling capabilities with modern UI design.

## Features

- **Automated Backup**: Intelligent file backup with incremental updates
- **USB Drive Monitoring**: Automatic detection and backup to USB drives
- **Backup Scheduling**: Flexible scheduling options for automated backups
- **File Encryption**: Secure your backups with AES encryption
- **Restore Functionality**: Easy restoration of backed up files
- **Modern UI**: Clean, intuitive interface built with Qt Quick/QML
- **Cross-Platform**: Supports Windows, macOS, and Linux

## Requirements

- Qt 6.5 or later
- CMake 3.16 or later (for CMake build)
- C++17 compatible compiler
- Operating System: Windows 10+, macOS 10.15+, or Linux

## Building

### Using CMake (Recommended)

```bash
mkdir build
cd build
cmake ..
cmake --build .
```

### Using QMake

```bash
qmake BackupPro.pro
make
```

## Project Structure

```
BackupPro/
├── src/                     # C++ source files
├── include/                 # C++ header files
├── resources/               # Application resources
│   ├── qml/                # QML UI files
│   ├── icons/              # Application icons
│   ├── themes/             # UI themes
│   └── translations/       # Internationalization
├── tests/                  # Unit tests
├── deployment/            # Deployment resources
└── docs/                  # Documentation
```

## Core Components

- **AppCore**: Main application logic and coordination
- **BackupManager**: Handles backup operations and file management
- **DriveWatcher**: Monitors USB drives and storage devices
- **FileOperations**: File system operations and utilities
- **Scheduler**: Backup scheduling and automation
- **Settings**: Application configuration management
- **Encryption**: Data encryption and security

## Usage

1. Launch BackupPro
2. Configure backup sources and destinations
3. Set up backup schedules (optional)
4. Monitor backup progress and history
5. Restore files when needed

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For support and documentation, please refer to the docs/ directory or create an issue on the project repository.
