#include "fileoperations.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDirIterator>
#include <QStorageInfo>
#include <QLoggingCategory>
#include <QThread>

Q_LOGGING_CATEGORY(fileOperations, "backuppro.fileoperations")

FileOperations::FileOperations(QObject *parent)
    : QObject(parent)
    , m_cancelRequested(false)
    , m_totalBytesToProcess(0)
    , m_bytesProcessed(0)
    , m_filesProcessed(0)
    , m_totalFilesToProcess(0)
{
    qCDebug(fileOperations) << "FileOperations created";
}

FileOperations::~FileOperations()
{
    qCDebug(fileOperations) << "FileOperations destroyed";
}

CopyResult FileOperations::copyFiles(const QString &sourcePath, const QString &destinationPath, 
                                    bool overwrite, bool preserveAttributes)
{
    QMutexLocker locker(&m_operationMutex);
    
    CopyResult result;
    result.success = false;
    result.bytesCopied = 0;
    result.filesCopied = 0;
    
    QFileInfo sourceInfo(sourcePath);
    if (!sourceInfo.exists()) {
        result.errorMessage = "Source path does not exist";
        return result;
    }
    
    if (sourceInfo.isFile()) {
        result.success = copyFileInternal(sourcePath, destinationPath, overwrite, preserveAttributes);
        if (result.success) {
            result.bytesCopied = sourceInfo.size();
            result.filesCopied = 1;
        }
    } else if (sourceInfo.isDir()) {
        QDir sourceDir(sourcePath);
        QDir destDir(destinationPath);
        
        if (!destDir.exists() && !destDir.mkpath(".")) {
            result.errorMessage = "Failed to create destination directory";
            return result;
        }
        
        result.success = copyDirectoryRecursive(sourceDir, destDir, overwrite);
    }
    
    return result;
}

CopyResult FileOperations::copyFilesRecursive(const QString &sourcePath, const QString &destinationPath, 
                                             bool overwrite)
{
    QMutexLocker locker(&m_operationMutex);
    
    m_cancelRequested = false;
    m_bytesProcessed = 0;
    m_filesProcessed = 0;
    
    emit statusChanged("Calculating total size...");
    
    // Calculate total size and file count
    m_totalBytesToProcess = getDirectorySize(sourcePath);
    m_totalFilesToProcess = getFileCount(sourcePath);
    
    emit statusChanged("Copying files...");
    
    CopyResult result = copyFiles(sourcePath, destinationPath, overwrite, true);
    
    if (result.success) {
        emit operationCompleted(true, "Copy operation completed successfully");
    } else {
        emit operationCompleted(false, result.errorMessage);
    }
    
    return result;
}

bool FileOperations::moveFiles(const QString &sourcePath, const QString &destinationPath)
{
    // First copy, then delete source if copy successful
    CopyResult copyResult = copyFiles(sourcePath, destinationPath, true, true);
    
    if (copyResult.success) {
        return deleteFiles(sourcePath, true);
    }
    
    return false;
}

bool FileOperations::deleteFiles(const QString &path, bool recursive)
{
    QFileInfo info(path);
    
    if (!info.exists()) {
        return true; // Already deleted
    }
    
    if (info.isFile()) {
        return QFile::remove(path);
    } else if (info.isDir() && recursive) {
        QDir dir(path);
        return dir.removeRecursively();
    }
    
    return false;
}

FileCompareResult FileOperations::compareFiles(const QString &file1Path, const QString &file2Path, 
                                              bool compareContent)
{
    FileCompareResult result;
    result.identical = false;
    
    QFileInfo info1(file1Path);
    QFileInfo info2(file2Path);
    
    if (!info1.exists() || !info2.exists()) {
        result.reason = "One or both files do not exist";
        return result;
    }
    
    result.sourceModified = info1.lastModified();
    result.destModified = info2.lastModified();
    result.sourceSize = info1.size();
    result.destSize = info2.size();
    
    // Quick size check
    if (info1.size() != info2.size()) {
        result.reason = "File sizes differ";
        return result;
    }
    
    // Quick timestamp check
    if (info1.lastModified() != info2.lastModified()) {
        result.reason = "Modification times differ";
        if (!compareContent) {
            return result;
        }
    }
    
    // Content comparison if requested
    if (compareContent) {
        QString hash1 = calculateChecksum(file1Path);
        QString hash2 = calculateChecksum(file2Path);
        
        if (hash1 != hash2) {
            result.reason = "File contents differ";
            return result;
        }
    }
    
    result.identical = true;
    result.reason = "Files are identical";
    return result;
}

QString FileOperations::calculateChecksum(const QString &filePath, QCryptographicHash::Algorithm algorithm)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(algorithm);
    
    const int bufferSize = 64 * 1024; // 64KB buffer
    while (!file.atEnd()) {
        QByteArray data = file.read(bufferSize);
        hash.addData(data);
        
        if (m_cancelRequested) {
            break;
        }
    }
    
    return hash.result().toHex();
}

qint64 FileOperations::getDirectorySize(const QString &dirPath)
{
    qint64 totalSize = 0;
    
    QDirIterator it(dirPath, QDir::Files | QDir::Hidden | QDir::System, QDirIterator::Subdirectories);
    while (it.hasNext()) {
        it.next();
        QFileInfo info = it.fileInfo();
        totalSize += info.size();
        
        if (m_cancelRequested) {
            break;
        }
    }
    
    return totalSize;
}

int FileOperations::getFileCount(const QString &dirPath)
{
    int count = 0;
    
    QDirIterator it(dirPath, QDir::Files | QDir::Hidden | QDir::System, QDirIterator::Subdirectories);
    while (it.hasNext()) {
        it.next();
        count++;
        
        if (m_cancelRequested) {
            break;
        }
    }
    
    return count;
}

QStringList FileOperations::getFileList(const QString &dirPath, bool recursive, const QStringList &filters)
{
    QStringList fileList;
    
    QDir::Filters dirFilters = QDir::Files | QDir::Hidden | QDir::System;
    QDirIterator::IteratorFlags flags = recursive ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags;
    
    QDirIterator it(dirPath, filters, dirFilters, flags);
    while (it.hasNext()) {
        it.next();
        fileList.append(it.filePath());
        
        if (m_cancelRequested) {
            break;
        }
    }
    
    return fileList;
}

bool FileOperations::pathExists(const QString &path)
{
    return QFileInfo::exists(path);
}

bool FileOperations::createDirectory(const QString &dirPath)
{
    QDir dir;
    return dir.mkpath(dirPath);
}

QFileInfo FileOperations::getFileInfo(const QString &filePath)
{
    return QFileInfo(filePath);
}

bool FileOperations::isFileNewer(const QString &file1Path, const QString &file2Path)
{
    QFileInfo info1(file1Path);
    QFileInfo info2(file2Path);
    
    if (!info1.exists() || !info2.exists()) {
        return false;
    }
    
    return info1.lastModified() > info2.lastModified();
}

bool FileOperations::setFilePermissions(const QString &filePath, QFile::Permissions permissions)
{
    return QFile::setPermissions(filePath, permissions);
}

qint64 FileOperations::getAvailableSpace(const QString &path)
{
    QStorageInfo storage(path);
    return storage.isValid() ? storage.bytesAvailable() : 0;
}

bool FileOperations::verifyFileIntegrity(const QString &filePath, const QString &expectedChecksum,
                                        QCryptographicHash::Algorithm algorithm)
{
    QString actualChecksum = calculateChecksum(filePath, algorithm);
    return actualChecksum.compare(expectedChecksum, Qt::CaseInsensitive) == 0;
}

void FileOperations::cancelOperation()
{
    m_cancelRequested = true;
    emit statusChanged("Cancelling operation...");
}

bool FileOperations::copyFileInternal(const QString &sourcePath, const QString &destinationPath, 
                                     bool overwrite, bool preserveAttributes)
{
    QFileInfo destInfo(destinationPath);
    if (destInfo.exists() && !overwrite) {
        return false;
    }
    
    // Remove destination file if it exists and we're overwriting
    if (destInfo.exists() && overwrite) {
        QFile::remove(destinationPath);
    }
    
    // Create destination directory if needed
    QDir destDir = destInfo.dir();
    if (!destDir.exists() && !destDir.mkpath(".")) {
        return false;
    }
    
    // Copy the file
    bool success = QFile::copy(sourcePath, destinationPath);
    
    if (success && preserveAttributes) {
        // Preserve file attributes
        QFileInfo sourceInfo(sourcePath);
        QFile destFile(destinationPath);
        destFile.setPermissions(sourceInfo.permissions());
    }
    
    if (success) {
        QFileInfo sourceInfo(sourcePath);
        updateProgress(sourceInfo.fileName(), sourceInfo.size());
    }
    
    return success;
}

bool FileOperations::copyDirectoryRecursive(const QDir &sourceDir, const QDir &destinationDir, bool overwrite)
{
    // Get list of files and directories
    QFileInfoList entries = sourceDir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot | QDir::Hidden | QDir::System);
    
    for (const QFileInfo &entry : entries) {
        if (m_cancelRequested) {
            return false;
        }
        
        QString sourcePath = entry.absoluteFilePath();
        QString destPath = destinationDir.absoluteFilePath(entry.fileName());
        
        if (entry.isDir()) {
            // Recursively copy subdirectory
            QDir subDestDir(destPath);
            if (!subDestDir.exists() && !subDestDir.mkpath(".")) {
                return false;
            }
            
            if (!copyDirectoryRecursive(QDir(sourcePath), QDir(destPath), overwrite)) {
                return false;
            }
        } else {
            // Copy file
            if (!copyFileInternal(sourcePath, destPath, overwrite, true)) {
                return false;
            }
        }
    }
    
    return true;
}

void FileOperations::updateProgress(const QString &fileName, qint64 bytesProcessed)
{
    m_bytesProcessed += bytesProcessed;
    m_filesProcessed++;
    
    emit progressChanged(fileName, bytesProcessed, m_totalBytesToProcess);
    
    if (m_filesProcessed % 10 == 0) { // Update status every 10 files
        emit statusChanged(QString("Processing: %1").arg(fileName));
    }
}
