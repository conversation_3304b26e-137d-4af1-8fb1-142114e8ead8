import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import BackupPro 1.0

Page {
    id: schedulePage
    
    property bool isDarkTheme: parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color accentColor: "#FF9800"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color successColor: "#4CAF50"
    readonly property color errorColor: "#F44336"
    
    background: Rectangle {
        color: backgroundColor
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 24
        
        ColumnLayout {
            width: parent.width
            spacing: 24
            
            // Page header
            RowLayout {
                Layout.fillWidth: true
                
                Label {
                    text: qsTr("Schedule")
                    font.pixelSize: 28
                    font.bold: true
                    color: textColor
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("New Schedule")
                    icon.source: "qrc:/icons/calendar.png"
                    
                    background: Rectangle {
                        color: parent.hovered ? Qt.darker(primaryColor, 1.1) : primaryColor
                        radius: 6
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                    
                    contentItem: RowLayout {
                        spacing: 8
                        
                        Image {
                            source: parent.parent.icon.source
                            Layout.preferredWidth: 16
                            Layout.preferredHeight: 16
                            fillMode: Image.PreserveAspectFit
                            
                            ColorOverlay {
                                anchors.fill: parent
                                source: parent
                                color: "white"
                            }
                        }
                        
                        Label {
                            text: parent.parent.text
                            color: "white"
                            font.pixelSize: 14
                        }
                    }
                    
                    onClicked: {
                        // TODO: Open new schedule dialog
                        console.log("Creating new schedule")
                    }
                }
            }
            
            // Scheduled backups list
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Scheduled Backups")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ListView {
                    anchors.fill: parent
                    anchors.margins: 16
                    model: ListModel {
                        id: scheduleModel
                        
                        ListElement {
                            name: "Daily Documents Backup"
                            sourcePath: "C:/Users/<USER>/Documents"
                            destinationPath: "D:/Backups/Documents"
                            schedule: "Daily at 2:00 AM"
                            enabled: true
                            nextRun: "Tomorrow at 2:00 AM"
                        }
                        
                        ListElement {
                            name: "Weekly Full System Backup"
                            sourcePath: "C:/"
                            destinationPath: "E:/SystemBackups"
                            schedule: "Weekly on Sunday at 3:00 AM"
                            enabled: false
                            nextRun: "Disabled"
                        }
                    }
                    
                    delegate: Rectangle {
                        width: parent.width
                        height: 80
                        color: "transparent"
                        border.color: borderColor
                        border.width: 1
                        radius: 6
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 16
                            
                            // Status indicator
                            Rectangle {
                                width: 12
                                height: 12
                                radius: 6
                                color: model.enabled ? successColor : secondaryTextColor
                                Layout.alignment: Qt.AlignVCenter
                            }
                            
                            // Schedule info
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4
                                
                                Label {
                                    text: model.name
                                    color: textColor
                                    font.pixelSize: 14
                                    font.bold: true
                                }
                                
                                Label {
                                    text: qsTr("%1 → %2").arg(model.sourcePath).arg(model.destinationPath)
                                    color: secondaryTextColor
                                    font.pixelSize: 11
                                    elide: Text.ElideMiddle
                                    Layout.fillWidth: true
                                }
                                
                                RowLayout {
                                    spacing: 16
                                    
                                    Label {
                                        text: model.schedule
                                        color: secondaryTextColor
                                        font.pixelSize: 11
                                    }
                                    
                                    Label {
                                        text: qsTr("Next: %1").arg(model.nextRun)
                                        color: model.enabled ? textColor : secondaryTextColor
                                        font.pixelSize: 11
                                        font.bold: model.enabled
                                    }
                                }
                            }
                            
                            // Action buttons
                            RowLayout {
                                spacing: 8
                                
                                Button {
                                    text: model.enabled ? qsTr("Disable") : qsTr("Enable")
                                    
                                    background: Rectangle {
                                        color: parent.hovered ? Qt.rgba(accentColor.r, accentColor.g, accentColor.b, 0.1) : "transparent"
                                        border.color: accentColor
                                        border.width: 1
                                        radius: 4
                                    }
                                    
                                    contentItem: Label {
                                        text: parent.text
                                        color: accentColor
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    
                                    onClicked: {
                                        // TODO: Toggle schedule enabled state
                                        console.log("Toggling schedule:", model.name)
                                    }
                                }
                                
                                Button {
                                    text: qsTr("Edit")
                                    
                                    background: Rectangle {
                                        color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                        border.color: primaryColor
                                        border.width: 1
                                        radius: 4
                                    }
                                    
                                    contentItem: Label {
                                        text: parent.text
                                        color: primaryColor
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    
                                    onClicked: {
                                        // TODO: Edit schedule
                                        console.log("Editing schedule:", model.name)
                                    }
                                }
                                
                                Button {
                                    text: qsTr("Delete")
                                    
                                    background: Rectangle {
                                        color: parent.hovered ? Qt.rgba(errorColor.r, errorColor.g, errorColor.b, 0.1) : "transparent"
                                        border.color: errorColor
                                        border.width: 1
                                        radius: 4
                                    }
                                    
                                    contentItem: Label {
                                        text: parent.text
                                        color: errorColor
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    
                                    onClicked: {
                                        // TODO: Delete schedule
                                        console.log("Deleting schedule:", model.name)
                                    }
                                }
                            }
                        }
                    }
                    
                    // Empty state
                    Label {
                        anchors.centerIn: parent
                        text: qsTr("No scheduled backups")
                        color: secondaryTextColor
                        font.pixelSize: 14
                        visible: parent.count === 0
                    }
                }
            }
            
            Item { Layout.fillHeight: true }
        }
    }
}
