import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import BackupPro 1.0

ApplicationWindow {
    id: mainWindow
    
    width: 1200
    height: 800
    minimumWidth: 900
    minimumHeight: 600
    
    title: qsTr("BackupPro - Professional Backup Solution")
    
    visible: true
    
    property alias currentPage: stackView.currentItem
    property bool isDarkTheme: appCore.settings.theme === 2 || 
                              (appCore.settings.theme === 0 && Qt.styleHints.colorScheme === Qt.Dark)
    
    // Color scheme
    readonly property color primaryColor: "#2196F3"
    readonly property color accentColor: "#FF9800"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    
    color: backgroundColor
    
    // System tray icon (if supported)
    property SystemTrayIcon systemTray: SystemTrayIcon {
        visible: appCore.settings.minimizeToTray
        icon.source: "qrc:/icons/app-icon.png"
        tooltip: "BackupPro"
        
        menu: Menu {
            MenuItem {
                text: qsTr("Show")
                onTriggered: {
                    mainWindow.show()
                    mainWindow.raise()
                    mainWindow.requestActivate()
                }
            }
            MenuItem {
                text: qsTr("Start Backup")
                onTriggered: stackView.push(backupPageComponent)
            }
            MenuSeparator {}
            MenuItem {
                text: qsTr("Quit")
                onTriggered: Qt.quit()
            }
        }
        
        onActivated: {
            if (reason === SystemTrayIcon.Trigger) {
                if (mainWindow.visible) {
                    mainWindow.hide()
                } else {
                    mainWindow.show()
                    mainWindow.raise()
                    mainWindow.requestActivate()
                }
            }
        }
    }
    
    // Header
    header: ToolBar {
        height: 60
        
        Rectangle {
            anchors.fill: parent
            color: surfaceColor
            border.color: borderColor
            border.width: 1
        }
        
        RowLayout {
            anchors.fill: parent
            anchors.margins: 16
            
            Image {
                source: "qrc:/icons/app-icon.png"
                width: 32
                height: 32
                fillMode: Image.PreserveAspectFit
            }
            
            Label {
                text: "BackupPro"
                font.pixelSize: 20
                font.bold: true
                color: textColor
            }
            
            Item { Layout.fillWidth: true }
            
            // Status indicator
            Rectangle {
                width: 12
                height: 12
                radius: 6
                color: appCore.isBackupInProgress ? accentColor : "#4CAF50"
                
                SequentialAnimation on opacity {
                    running: appCore.isBackupInProgress
                    loops: Animation.Infinite
                    NumberAnimation { to: 0.3; duration: 500 }
                    NumberAnimation { to: 1.0; duration: 500 }
                }
            }
            
            Label {
                text: appCore.isBackupInProgress ? qsTr("Backup in progress...") : qsTr("Ready")
                color: secondaryTextColor
                font.pixelSize: 12
            }
            
            ToolButton {
                icon.source: "qrc:/icons/settings.png"
                icon.color: textColor
                onClicked: stackView.push(settingsPageComponent)
                
                ToolTip.visible: hovered
                ToolTip.text: qsTr("Settings")
            }
        }
    }
    
    // Main content area
    RowLayout {
        anchors.fill: parent
        spacing: 0
        
        // Navigation sidebar
        Rectangle {
            Layout.preferredWidth: 200
            Layout.fillHeight: true
            color: surfaceColor
            border.color: borderColor
            border.width: 1
            
            Column {
                anchors.fill: parent
                anchors.margins: 8
                spacing: 4
                
                NavButton {
                    text: qsTr("Backup")
                    icon: "qrc:/icons/backup.png"
                    active: stackView.currentItem && stackView.currentItem.objectName === "backupPage"
                    onClicked: stackView.push(backupPageComponent)
                }
                
                NavButton {
                    text: qsTr("Restore")
                    icon: "qrc:/icons/restore.png"
                    active: stackView.currentItem && stackView.currentItem.objectName === "restorePage"
                    onClicked: stackView.push(restorePageComponent)
                }
                
                NavButton {
                    text: qsTr("Schedule")
                    icon: "qrc:/icons/schedule.png"
                    active: stackView.currentItem && stackView.currentItem.objectName === "schedulePage"
                    onClicked: stackView.push(schedulePageComponent)
                }
                
                NavButton {
                    text: qsTr("History")
                    icon: "qrc:/icons/history.png"
                    active: stackView.currentItem && stackView.currentItem.objectName === "historyPage"
                    onClicked: stackView.push(historyPageComponent)
                }
                
                Rectangle {
                    width: parent.width
                    height: 1
                    color: borderColor
                    anchors.margins: 8
                }
                
                NavButton {
                    text: qsTr("Settings")
                    icon: "qrc:/icons/settings.png"
                    active: stackView.currentItem && stackView.currentItem.objectName === "settingsPage"
                    onClicked: stackView.push(settingsPageComponent)
                }
            }
        }
        
        // Main content stack
        StackView {
            id: stackView
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            initialItem: backupPageComponent
            
            // Page transition animations
            pushEnter: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 0
                    to: 1
                    duration: 200
                }
                PropertyAnimation {
                    property: "x"
                    from: stackView.width
                    to: 0
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            }
            
            pushExit: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 1
                    to: 0
                    duration: 200
                }
                PropertyAnimation {
                    property: "x"
                    from: 0
                    to: -stackView.width
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            }
            
            popEnter: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 0
                    to: 1
                    duration: 200
                }
                PropertyAnimation {
                    property: "x"
                    from: -stackView.width
                    to: 0
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            }
            
            popExit: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 1
                    to: 0
                    duration: 200
                }
                PropertyAnimation {
                    property: "x"
                    from: 0
                    to: stackView.width
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            }
        }
    }
    
    // Status bar
    footer: ToolBar {
        height: 30
        
        Rectangle {
            anchors.fill: parent
            color: surfaceColor
            border.color: borderColor
            border.width: 1
        }
        
        RowLayout {
            anchors.fill: parent
            anchors.margins: 8
            
            Label {
                text: appCore.statusMessage
                color: secondaryTextColor
                font.pixelSize: 11
                elide: Text.ElideRight
                Layout.fillWidth: true
            }
            
            // Progress indicator
            ProgressBar {
                Layout.preferredWidth: 150
                visible: appCore.isBackupInProgress
                value: appCore.backupProgress / 100.0
                
                background: Rectangle {
                    color: borderColor
                    radius: 2
                }
                
                contentItem: Rectangle {
                    color: primaryColor
                    radius: 2
                }
            }
            
            Label {
                visible: appCore.isBackupInProgress
                text: appCore.backupProgress + "%"
                color: secondaryTextColor
                font.pixelSize: 11
            }
        }
    }
    
    // Page components
    Component {
        id: backupPageComponent
        BackupPage {
            objectName: "backupPage"
        }
    }
    
    Component {
        id: restorePageComponent
        RestorePage {
            objectName: "restorePage"
        }
    }
    
    Component {
        id: schedulePageComponent
        SchedulePage {
            objectName: "schedulePage"
        }
    }
    
    Component {
        id: historyPageComponent
        HistoryPage {
            objectName: "historyPage"
        }
    }
    
    Component {
        id: settingsPageComponent
        SettingsPage {
            objectName: "settingsPage"
        }
    }
    
    // Connections to backend
    Connections {
        target: appCore
        
        function onBackupStarted(sourcePath, destinationPath) {
            if (appCore.settings.showNotifications) {
                systemTray.showMessage(
                    qsTr("Backup Started"),
                    qsTr("Backing up %1 to %2").arg(sourcePath).arg(destinationPath),
                    SystemTrayIcon.Information,
                    5000
                )
            }
        }
        
        function onBackupCompleted(success, message) {
            if (appCore.settings.showNotifications) {
                systemTray.showMessage(
                    success ? qsTr("Backup Completed") : qsTr("Backup Failed"),
                    message,
                    success ? SystemTrayIcon.Information : SystemTrayIcon.Critical,
                    5000
                )
            }
        }
        
        function onDriveConnected(drivePath, driveInfo) {
            if (appCore.settings.showNotifications) {
                systemTray.showMessage(
                    qsTr("Drive Connected"),
                    qsTr("Drive %1 connected").arg(driveInfo.displayName),
                    SystemTrayIcon.Information,
                    3000
                )
            }
        }
    }
    
    // Window state management
    Component.onCompleted: {
        // Restore window geometry if available
        if (appCore.settings.windowGeometry.length > 0) {
            // Restore geometry from settings
        }
    }
    
    onClosing: {
        if (appCore.settings.minimizeToTray && systemTray.available) {
            close.accepted = false
            hide()
        } else {
            // Save window geometry
            appCore.settings.setWindowGeometry(/* window geometry */)
        }
    }
}
