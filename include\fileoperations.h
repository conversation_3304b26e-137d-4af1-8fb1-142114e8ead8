#ifndef FILEOPERATIONS_H
#define FILEOPERATIONS_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>
#include <QCryptographicHash>
#include <QMutex>
#include <QThread>

/**
 * @brief File copy operation result
 */
struct CopyResult {
    bool success;
    QString errorMessage;
    qint64 bytesCopied;
    int filesCopied;
    QStringList failedFiles;
};

/**
 * @brief File comparison result
 */
struct FileCompareResult {
    bool identical;
    QString reason;
    QDateTime sourceModified;
    QDateTime destModified;
    qint64 sourceSize;
    qint64 destSize;
};

/**
 * @brief The FileOperations class handles file system operations
 * 
 * This class provides comprehensive file operations including copying,
 * moving, deleting, comparing files, calculating checksums, and managing
 * file permissions and attributes.
 */
class FileOperations : public QObject
{
    Q_OBJECT

public:
    explicit FileOperations(QObject *parent = nullptr);
    ~FileOperations();

    /**
     * @brief Copy files from source to destination
     * @param sourcePath Source file or directory path
     * @param destinationPath Destination path
     * @param overwrite Whether to overwrite existing files
     * @param preserveAttributes Whether to preserve file attributes
     * @return CopyResult with operation details
     */
    CopyResult copyFiles(const QString &sourcePath, const QString &destinationPath, 
                        bool overwrite = true, bool preserveAttributes = true);

    /**
     * @brief Copy files recursively with progress reporting
     * @param sourcePath Source directory path
     * @param destinationPath Destination directory path
     * @param overwrite Whether to overwrite existing files
     * @return CopyResult with operation details
     */
    CopyResult copyFilesRecursive(const QString &sourcePath, const QString &destinationPath, 
                                 bool overwrite = true);

    /**
     * @brief Move files from source to destination
     * @param sourcePath Source file or directory path
     * @param destinationPath Destination path
     * @return true if move successful
     */
    bool moveFiles(const QString &sourcePath, const QString &destinationPath);

    /**
     * @brief Delete files or directories
     * @param path Path to delete
     * @param recursive Whether to delete recursively
     * @return true if deletion successful
     */
    bool deleteFiles(const QString &path, bool recursive = true);

    /**
     * @brief Compare two files
     * @param file1Path Path to first file
     * @param file2Path Path to second file
     * @param compareContent Whether to compare file content (slower)
     * @return FileCompareResult with comparison details
     */
    FileCompareResult compareFiles(const QString &file1Path, const QString &file2Path, 
                                  bool compareContent = false);

    /**
     * @brief Calculate file checksum
     * @param filePath Path to file
     * @param algorithm Hash algorithm to use
     * @return Checksum as hex string
     */
    QString calculateChecksum(const QString &filePath, 
                             QCryptographicHash::Algorithm algorithm = QCryptographicHash::Sha256);

    /**
     * @brief Get directory size recursively
     * @param dirPath Directory path
     * @return Total size in bytes
     */
    qint64 getDirectorySize(const QString &dirPath);

    /**
     * @brief Get file count in directory recursively
     * @param dirPath Directory path
     * @return Total number of files
     */
    int getFileCount(const QString &dirPath);

    /**
     * @brief Get list of files in directory
     * @param dirPath Directory path
     * @param recursive Whether to search recursively
     * @param filters File name filters
     * @return List of file paths
     */
    QStringList getFileList(const QString &dirPath, bool recursive = true, 
                           const QStringList &filters = QStringList());

    /**
     * @brief Check if path exists
     * @param path Path to check
     * @return true if path exists
     */
    bool pathExists(const QString &path);

    /**
     * @brief Create directory structure
     * @param dirPath Directory path to create
     * @return true if creation successful
     */
    bool createDirectory(const QString &dirPath);

    /**
     * @brief Get file information
     * @param filePath Path to file
     * @return QFileInfo object
     */
    QFileInfo getFileInfo(const QString &filePath);

    /**
     * @brief Check if file is newer than another
     * @param file1Path Path to first file
     * @param file2Path Path to second file
     * @return true if file1 is newer than file2
     */
    bool isFileNewer(const QString &file1Path, const QString &file2Path);

    /**
     * @brief Set file permissions
     * @param filePath Path to file
     * @param permissions File permissions
     * @return true if successful
     */
    bool setFilePermissions(const QString &filePath, QFile::Permissions permissions);

    /**
     * @brief Get available space on drive containing path
     * @param path Path to check
     * @return Available space in bytes
     */
    qint64 getAvailableSpace(const QString &path);

    /**
     * @brief Verify file integrity using checksum
     * @param filePath Path to file
     * @param expectedChecksum Expected checksum
     * @param algorithm Hash algorithm used
     * @return true if file is valid
     */
    bool verifyFileIntegrity(const QString &filePath, const QString &expectedChecksum,
                            QCryptographicHash::Algorithm algorithm = QCryptographicHash::Sha256);

public slots:
    /**
     * @brief Cancel current operation
     */
    void cancelOperation();

signals:
    /**
     * @brief Emitted during file operations to report progress
     * @param fileName Current file being processed
     * @param bytesProcessed Bytes processed so far
     * @param totalBytes Total bytes to process
     */
    void progressChanged(const QString &fileName, qint64 bytesProcessed, qint64 totalBytes);

    /**
     * @brief Emitted when operation status changes
     * @param status Current status message
     */
    void statusChanged(const QString &status);

    /**
     * @brief Emitted when an error occurs
     * @param error Error message
     */
    void errorOccurred(const QString &error);

    /**
     * @brief Emitted when operation completes
     * @param success Whether operation was successful
     * @param message Result message
     */
    void operationCompleted(bool success, const QString &message);

private:
    bool copyFileInternal(const QString &sourcePath, const QString &destinationPath, 
                         bool overwrite, bool preserveAttributes);
    bool copyDirectoryRecursive(const QDir &sourceDir, const QDir &destinationDir, 
                               bool overwrite);
    void updateProgress(const QString &fileName, qint64 bytesProcessed);
    
    // Operation state
    bool m_cancelRequested;
    qint64 m_totalBytesToProcess;
    qint64 m_bytesProcessed;
    int m_filesProcessed;
    int m_totalFilesToProcess;
    
    mutable QMutex m_operationMutex;
};

Q_DECLARE_METATYPE(CopyResult)
Q_DECLARE_METATYPE(FileCompareResult)

#endif // FILEOPERATIONS_H
