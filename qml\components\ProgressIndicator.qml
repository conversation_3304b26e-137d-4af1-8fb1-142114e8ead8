import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Item {
    id: progressIndicator
    
    property int progress: 0
    property string statusText: ""
    property bool showCancel: true
    
    signal cancelRequested()
    
    property bool isDarkTheme: parent.parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color accentColor: "#FF9800"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color errorColor: "#F44336"
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 16
        
        // Status text
        Label {
            text: progressIndicator.statusText || qsTr("Processing...")
            color: textColor
            font.pixelSize: 14
            Layout.fillWidth: true
            elide: Text.ElideRight
        }
        
        // Progress bar
        RowLayout {
            Layout.fillWidth: true
            spacing: 12
            
            ProgressBar {
                id: progressBar
                Layout.fillWidth: true
                value: progressIndicator.progress / 100.0
                
                background: Rectangle {
                    implicitWidth: 200
                    implicitHeight: 8
                    color: backgroundColor
                    radius: 4
                    border.color: borderColor
                    border.width: 1
                }
                
                contentItem: Item {
                    implicitWidth: 200
                    implicitHeight: 6
                    
                    Rectangle {
                        width: progressBar.visualPosition * parent.width
                        height: parent.height
                        radius: 3
                        color: primaryColor
                        
                        // Animated gradient effect
                        Rectangle {
                            anchors.fill: parent
                            radius: parent.radius
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: Qt.rgba(1, 1, 1, 0.3) }
                                GradientStop { position: 0.5; color: Qt.rgba(1, 1, 1, 0.1) }
                                GradientStop { position: 1.0; color: Qt.rgba(0, 0, 0, 0.1) }
                            }
                        }
                        
                        // Animated shine effect
                        Rectangle {
                            id: shineEffect
                            width: 30
                            height: parent.height
                            radius: parent.radius
                            gradient: Gradient {
                                orientation: Gradient.Horizontal
                                GradientStop { position: 0.0; color: Qt.rgba(1, 1, 1, 0) }
                                GradientStop { position: 0.5; color: Qt.rgba(1, 1, 1, 0.4) }
                                GradientStop { position: 1.0; color: Qt.rgba(1, 1, 1, 0) }
                            }
                            
                            SequentialAnimation on x {
                                running: progressIndicator.progress > 0 && progressIndicator.progress < 100
                                loops: Animation.Infinite
                                
                                NumberAnimation {
                                    from: -shineEffect.width
                                    to: progressBar.width
                                    duration: 2000
                                    easing.type: Easing.InOutQuad
                                }
                                
                                PauseAnimation { duration: 1000 }
                            }
                        }
                    }
                }
            }
            
            Label {
                text: progressIndicator.progress + "%"
                color: textColor
                font.pixelSize: 14
                font.bold: true
                Layout.preferredWidth: 40
                horizontalAlignment: Text.AlignRight
            }
        }
        
        // Additional info and controls
        RowLayout {
            Layout.fillWidth: true
            spacing: 16
            
            // Time estimation (placeholder)
            Label {
                text: qsTr("Estimated time: %1").arg(calculateTimeRemaining())
                color: secondaryTextColor
                font.pixelSize: 12
                Layout.fillWidth: true
            }
            
            // Cancel button
            Button {
                visible: progressIndicator.showCancel
                text: qsTr("Cancel")
                
                background: Rectangle {
                    color: parent.hovered ? Qt.rgba(errorColor.r, errorColor.g, errorColor.b, 0.1) : "transparent"
                    border.color: errorColor
                    border.width: 1
                    radius: 4
                }
                
                contentItem: Label {
                    text: parent.text
                    color: errorColor
                    font.pixelSize: 12
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: progressIndicator.cancelRequested()
            }
        }
        
        // Detailed progress info
        Rectangle {
            Layout.fillWidth: true
            height: detailsColumn.implicitHeight + 16
            color: backgroundColor
            border.color: borderColor
            border.width: 1
            radius: 6
            
            ColumnLayout {
                id: detailsColumn
                anchors.fill: parent
                anchors.margins: 8
                spacing: 4
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Label {
                        text: qsTr("Files processed:")
                        color: secondaryTextColor
                        font.pixelSize: 11
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Label {
                        text: "0 / 0" // Placeholder - would be connected to actual data
                        color: textColor
                        font.pixelSize: 11
                        font.bold: true
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Label {
                        text: qsTr("Data transferred:")
                        color: secondaryTextColor
                        font.pixelSize: 11
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Label {
                        text: "0 MB / 0 MB" // Placeholder - would be connected to actual data
                        color: textColor
                        font.pixelSize: 11
                        font.bold: true
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Label {
                        text: qsTr("Speed:")
                        color: secondaryTextColor
                        font.pixelSize: 11
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Label {
                        text: "0 MB/s" // Placeholder - would be connected to actual data
                        color: textColor
                        font.pixelSize: 11
                        font.bold: true
                    }
                }
            }
        }
    }
    
    function calculateTimeRemaining() {
        if (progressIndicator.progress <= 0) {
            return qsTr("Calculating...")
        }
        
        if (progressIndicator.progress >= 100) {
            return qsTr("Complete")
        }
        
        // Placeholder calculation - in real implementation would use actual transfer rates
        var remainingPercent = 100 - progressIndicator.progress
        var estimatedMinutes = Math.ceil(remainingPercent / 10) // Rough estimate
        
        if (estimatedMinutes < 1) {
            return qsTr("Less than 1 minute")
        } else if (estimatedMinutes === 1) {
            return qsTr("About 1 minute")
        } else if (estimatedMinutes < 60) {
            return qsTr("About %1 minutes").arg(estimatedMinutes)
        } else {
            var hours = Math.floor(estimatedMinutes / 60)
            var minutes = estimatedMinutes % 60
            if (hours === 1) {
                return minutes > 0 ? qsTr("About 1 hour %1 minutes").arg(minutes) : qsTr("About 1 hour")
            } else {
                return minutes > 0 ? qsTr("About %1 hours %2 minutes").arg(hours).arg(minutes) : qsTr("About %1 hours").arg(hours)
            }
        }
    }
}
