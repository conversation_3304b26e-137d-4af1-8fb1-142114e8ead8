cmake_minimum_required(VERSION 3.21)

project(BackupProTests)

# Find Qt6 Test module
find_package(Qt6 REQUIRED COMPONENTS Test Core)

# Enable Qt's automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Include directories
include_directories(../include)

# Test executable for Settings class
add_executable(test_settings
    test_settings.cpp
    ../src/settings.cpp
)

target_link_libraries(test_settings
    Qt6::Test
    Qt6::Core
)

# Test executable for FileOperations class
add_executable(test_fileoperations
    test_fileoperations.cpp
    ../src/fileoperations.cpp
)

target_link_libraries(test_fileoperations
    Qt6::Test
    Qt6::Core
    Qt6::Concurrent
)

# Test executable for Encryption class
add_executable(test_encryption
    test_encryption.cpp
    ../src/encryption.cpp
)

target_link_libraries(test_encryption
    Qt6::Test
    Qt6::Core
)

# Add tests to CTest
enable_testing()

add_test(NAME SettingsTest COMMAND test_settings)
add_test(NAME FileOperationsTest COMMAND test_fileoperations)
add_test(NAME EncryptionTest COMMAND test_encryption)

# Set test properties
set_tests_properties(SettingsTest PROPERTIES TIMEOUT 30)
set_tests_properties(FileOperationsTest PROPERTIES TIMEOUT 60)
set_tests_properties(EncryptionTest PROPERTIES TIMEOUT 30)
