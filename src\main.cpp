#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QIcon>
#include <QDir>
#include <QStandardPaths>
#include <QLoggingCategory>
#include <QDebug>

#include "appcore.h"

Q_LOGGING_CATEGORY(backupProMain, "backuppro.main")

int main(int argc, char *argv[])
{
    // Set application properties
    QGuiApplication::setApplicationName("BackupPro");
    QGuiApplication::setApplicationVersion("1.0.0");
    QGuiApplication::setOrganizationName("BackupPro");
    QGuiApplication::setOrganizationDomain("backuppro.com");

    QGuiApplication app(argc, argv);

    // Set application icon
    app.setWindowIcon(QIcon(":/resources/icons/app-icon.png"));

    // Enable high DPI support
    QGuiApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QGuiApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);

    // Create application data directory if it doesn't exist
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDataDir(appDataPath);
    if (!appDataDir.exists()) {
        if (!appDataDir.mkpath(".")) {
            qCCritical(backupProMain) << "Failed to create application data directory:" << appDataPath;
            return -1;
        }
    }

    qCInfo(backupProMain) << "BackupPro starting...";
    qCInfo(backupProMain) << "Application data directory:" << appDataPath;

    // Create and initialize the application core
    AppCore appCore;
    if (!appCore.initialize()) {
        qCCritical(backupProMain) << "Failed to initialize application core";
        return -1;
    }

    // Start the QML engine
    if (!appCore.startEngine()) {
        qCCritical(backupProMain) << "Failed to start QML engine";
        return -1;
    }

    qCInfo(backupProMain) << "BackupPro started successfully";

    // Run the application
    int result = app.exec();

    qCInfo(backupProMain) << "BackupPro shutting down with exit code:" << result;
    return result;
}
