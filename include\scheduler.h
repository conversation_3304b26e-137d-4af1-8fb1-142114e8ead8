#ifndef SCHEDULER_H
#define SCHEDULER_H

#include <QObject>
#include <QTimer>
#include <QDateTime>
#include <QVariantList>
#include <QVariantMap>
#include <QJsonObject>
#include <QJsonArray>

/**
 * @brief Backup schedule types
 */
enum class ScheduleType {
    Once,
    Daily,
    Weekly,
    Monthly,
    Custom
};

/**
 * @brief Backup schedule structure
 */
struct BackupSchedule {
    QString id;
    QString name;
    QString sourcePath;
    QString destinationPath;
    ScheduleType type;
    QDateTime nextRun;
    QDateTime lastRun;
    bool enabled;
    bool encrypted;
    int intervalHours;  // For custom intervals
    QList<int> weekDays; // For weekly schedules (1=Monday, 7=Sunday)
    int monthDay;       // For monthly schedules (1-31)
    QTime timeOfDay;    // Time to run the backup
    QString description;
    QVariantMap options; // Additional options
};

/**
 * @brief The Scheduler class manages backup scheduling and automation
 * 
 * This class provides comprehensive backup scheduling functionality including
 * one-time, recurring, and custom interval schedules. It manages schedule
 * persistence, execution, and provides notifications for scheduled events.
 */
class Scheduler : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariantList schedules READ getSchedulesAsVariant NOTIFY schedulesChanged)

public:
    explicit Scheduler(QObject *parent = nullptr);
    ~Scheduler();

    /**
     * @brief Initialize the scheduler
     * @return true if initialization successful
     */
    bool initialize();

    /**
     * @brief Start the scheduler
     */
    void start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Add a new backup schedule
     * @param schedule Backup schedule to add
     * @return Schedule ID if successful, empty string otherwise
     */
    QString addSchedule(const BackupSchedule &schedule);

    /**
     * @brief Remove a backup schedule
     * @param scheduleId Schedule ID to remove
     * @return true if removal successful
     */
    bool removeSchedule(const QString &scheduleId);

    /**
     * @brief Update an existing schedule
     * @param scheduleId Schedule ID to update
     * @param schedule Updated schedule data
     * @return true if update successful
     */
    bool updateSchedule(const QString &scheduleId, const BackupSchedule &schedule);

    /**
     * @brief Get schedule by ID
     * @param scheduleId Schedule ID
     * @return BackupSchedule or empty schedule if not found
     */
    BackupSchedule getSchedule(const QString &scheduleId) const;

    /**
     * @brief Get all schedules
     * @return List of all backup schedules
     */
    QList<BackupSchedule> getSchedules() const;

    /**
     * @brief Get schedules as QVariantList for QML
     * @return QVariantList of schedules
     */
    QVariantList getSchedulesAsVariant() const;

    /**
     * @brief Enable or disable a schedule
     * @param scheduleId Schedule ID
     * @param enabled Whether to enable the schedule
     * @return true if successful
     */
    bool setScheduleEnabled(const QString &scheduleId, bool enabled);

    /**
     * @brief Run a schedule immediately
     * @param scheduleId Schedule ID to run
     * @return true if schedule was triggered
     */
    bool runScheduleNow(const QString &scheduleId);

    /**
     * @brief Get next scheduled backup time
     * @return QDateTime of next scheduled backup
     */
    QDateTime getNextScheduledTime() const;

    /**
     * @brief Check if scheduler is running
     * @return true if scheduler is active
     */
    bool isRunning() const;

public slots:
    /**
     * @brief Create a daily backup schedule
     * @param name Schedule name
     * @param sourcePath Source directory
     * @param destinationPath Destination directory
     * @param timeOfDay Time to run backup
     * @param encrypted Whether to encrypt backup
     * @return Schedule ID
     */
    Q_INVOKABLE QString createDailySchedule(const QString &name, const QString &sourcePath,
                                           const QString &destinationPath, const QTime &timeOfDay,
                                           bool encrypted = false);

    /**
     * @brief Create a weekly backup schedule
     * @param name Schedule name
     * @param sourcePath Source directory
     * @param destinationPath Destination directory
     * @param weekDays Days of week to run (1=Monday, 7=Sunday)
     * @param timeOfDay Time to run backup
     * @param encrypted Whether to encrypt backup
     * @return Schedule ID
     */
    Q_INVOKABLE QString createWeeklySchedule(const QString &name, const QString &sourcePath,
                                            const QString &destinationPath, const QList<int> &weekDays,
                                            const QTime &timeOfDay, bool encrypted = false);

    /**
     * @brief Create a custom interval schedule
     * @param name Schedule name
     * @param sourcePath Source directory
     * @param destinationPath Destination directory
     * @param intervalHours Interval in hours
     * @param encrypted Whether to encrypt backup
     * @return Schedule ID
     */
    Q_INVOKABLE QString createCustomSchedule(const QString &name, const QString &sourcePath,
                                            const QString &destinationPath, int intervalHours,
                                            bool encrypted = false);

    /**
     * @brief Delete a schedule
     * @param scheduleId Schedule ID to delete
     */
    Q_INVOKABLE void deleteSchedule(const QString &scheduleId);

    /**
     * @brief Toggle schedule enabled state
     * @param scheduleId Schedule ID
     */
    Q_INVOKABLE void toggleSchedule(const QString &scheduleId);

signals:
    /**
     * @brief Emitted when schedules list changes
     */
    void schedulesChanged();

    /**
     * @brief Emitted when a scheduled backup should start
     * @param schedule The schedule that triggered
     */
    void scheduleTriggered(const BackupSchedule &schedule);

    /**
     * @brief Emitted when a schedule is added
     * @param scheduleId ID of added schedule
     */
    void scheduleAdded(const QString &scheduleId);

    /**
     * @brief Emitted when a schedule is removed
     * @param scheduleId ID of removed schedule
     */
    void scheduleRemoved(const QString &scheduleId);

    /**
     * @brief Emitted when a schedule is updated
     * @param scheduleId ID of updated schedule
     */
    void scheduleUpdated(const QString &scheduleId);

    /**
     * @brief Emitted when next scheduled time changes
     * @param nextTime Next scheduled backup time
     */
    void nextScheduleChanged(const QDateTime &nextTime);

private slots:
    void checkSchedules();
    void onScheduleTimer();

private:
    void loadSchedules();
    void saveSchedules();
    void calculateNextRun(BackupSchedule &schedule);
    void updateNextScheduleTimer();
    QString generateScheduleId() const;
    QJsonObject scheduleToJson(const BackupSchedule &schedule) const;
    BackupSchedule scheduleFromJson(const QJsonObject &json) const;
    QVariantMap scheduleToVariantMap(const BackupSchedule &schedule) const;

    QTimer *m_checkTimer;
    QTimer *m_nextScheduleTimer;
    QList<BackupSchedule> m_schedules;
    bool m_isRunning;
    QString m_schedulesFilePath;
};

Q_DECLARE_METATYPE(ScheduleType)
Q_DECLARE_METATYPE(BackupSchedule)

#endif // SCHEDULER_H
