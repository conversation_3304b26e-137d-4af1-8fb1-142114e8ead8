#ifndef DRIVEWATCHER_H
#define DRIVEWATCHER_H

#include <QObject>
#include <QStringList>
#include <QTimer>
#include <QStorageInfo>
#include <QFileSystemWatcher>
#include <QVariantMap>

/**
 * @brief Drive information structure
 */
struct DriveInfo {
    QString path;
    QString name;
    QString fileSystem;
    qint64 totalSpace;
    qint64 availableSpace;
    bool isRemovable;
    bool isReady;
    QString displayName;
};

/**
 * @brief The DriveWatcher class monitors USB drives and storage devices
 * 
 * This class provides real-time monitoring of connected storage devices,
 * detects when USB drives are connected or disconnected, and provides
 * information about available storage space and drive properties.
 */
class DriveWatcher : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QStringList availableDrives READ availableDrives NOTIFY drivesChanged)

public:
    explicit DriveWatcher(QObject *parent = nullptr);
    ~DriveWatcher();

    /**
     * @brief Start monitoring drives
     */
    void startMonitoring();

    /**
     * @brief Stop monitoring drives
     */
    void stopMonitoring();

    /**
     * @brief Get list of available drive paths
     * @return QStringList of drive paths
     */
    QStringList availableDrives() const;

    /**
     * @brief Get detailed information about all drives
     * @return QVariantList containing drive information
     */
    Q_INVOKABLE QVariantList getDriveInfo() const;

    /**
     * @brief Get information about a specific drive
     * @param drivePath Path to the drive
     * @return QVariantMap containing drive information
     */
    Q_INVOKABLE QVariantMap getDriveInfo(const QString &drivePath) const;

    /**
     * @brief Check if a drive is removable (USB, external)
     * @param drivePath Path to the drive
     * @return true if drive is removable
     */
    Q_INVOKABLE bool isRemovableDrive(const QString &drivePath) const;

    /**
     * @brief Check if a drive has enough space for backup
     * @param drivePath Path to the drive
     * @param requiredSpace Required space in bytes
     * @return true if drive has enough space
     */
    Q_INVOKABLE bool hasEnoughSpace(const QString &drivePath, qint64 requiredSpace) const;

    /**
     * @brief Get available space on drive
     * @param drivePath Path to the drive
     * @return Available space in bytes
     */
    Q_INVOKABLE qint64 getAvailableSpace(const QString &drivePath) const;

    /**
     * @brief Get total space on drive
     * @param drivePath Path to the drive
     * @return Total space in bytes
     */
    Q_INVOKABLE qint64 getTotalSpace(const QString &drivePath) const;

    /**
     * @brief Get user-friendly drive name
     * @param drivePath Path to the drive
     * @return Display name for the drive
     */
    Q_INVOKABLE QString getDriveName(const QString &drivePath) const;

    /**
     * @brief Check if drive is ready for operations
     * @param drivePath Path to the drive
     * @return true if drive is ready
     */
    Q_INVOKABLE bool isDriveReady(const QString &drivePath) const;

public slots:
    /**
     * @brief Refresh drive list manually
     */
    void refreshDrives();

    /**
     * @brief Enable/disable automatic USB backup
     * @param enabled Whether to enable auto backup
     * @param backupPath Default backup source path
     */
    void setAutoBackupEnabled(bool enabled, const QString &backupPath = QString());

signals:
    /**
     * @brief Emitted when drive list changes
     */
    void drivesChanged();

    /**
     * @brief Emitted when a new drive is connected
     * @param drivePath Path to the connected drive
     * @param driveInfo Information about the drive
     */
    void driveConnected(const QString &drivePath, const QVariantMap &driveInfo);

    /**
     * @brief Emitted when a drive is disconnected
     * @param drivePath Path to the disconnected drive
     */
    void driveDisconnected(const QString &drivePath);

    /**
     * @brief Emitted when a removable drive is connected and auto-backup is enabled
     * @param drivePath Path to the drive
     */
    void autoBackupTriggered(const QString &drivePath);

    /**
     * @brief Emitted when drive space is low
     * @param drivePath Path to the drive
     * @param availableSpace Available space in bytes
     */
    void lowSpaceWarning(const QString &drivePath, qint64 availableSpace);

private slots:
    void checkDrives();
    void onDirectoryChanged(const QString &path);

private:
    DriveInfo getDriveInfoInternal(const QString &drivePath) const;
    QVariantMap driveInfoToVariantMap(const DriveInfo &info) const;
    void detectDriveChanges();
    QString formatBytes(qint64 bytes) const;

    QTimer *m_monitorTimer;
    QFileSystemWatcher *m_fileSystemWatcher;
    QStringList m_currentDrives;
    QList<DriveInfo> m_driveInfoCache;
    
    bool m_isMonitoring;
    bool m_autoBackupEnabled;
    QString m_autoBackupSourcePath;
    
    // Platform-specific monitoring
#ifdef Q_OS_WIN
    void setupWindowsMonitoring();
    void cleanupWindowsMonitoring();
#elif defined(Q_OS_LINUX)
    void setupLinuxMonitoring();
    void cleanupLinuxMonitoring();
#elif defined(Q_OS_MACOS)
    void setupMacOSMonitoring();
    void cleanupMacOSMonitoring();
#endif
};

Q_DECLARE_METATYPE(DriveInfo)

#endif // DRIVEWATCHER_H
