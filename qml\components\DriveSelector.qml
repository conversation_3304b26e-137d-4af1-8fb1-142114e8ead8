import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import BackupPro 1.0

ComboBox {
    id: driveSelector
    
    signal driveSelected(string drivePath)
    
    property bool isDarkTheme: parent.parent.parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    
    width: 120
    
    model: ListModel {
        id: driveModel
    }
    
    displayText: count > 0 ? qsTr("Drives (%1)").arg(count) : qsTr("No Drives")
    
    background: Rectangle {
        color: surfaceColor
        border.color: parent.activeFocus ? primaryColor : borderColor
        border.width: parent.activeFocus ? 2 : 1
        radius: 4
        
        Behavior on border.color {
            ColorAnimation { duration: 150 }
        }
    }
    
    contentItem: RowLayout {
        spacing: 8
        
        Image {
            source: "qrc:/icons/drive.png"
            Layout.preferredWidth: 16
            Layout.preferredHeight: 16
            fillMode: Image.PreserveAspectFit
            
            ColorOverlay {
                anchors.fill: parent
                source: parent
                color: textColor
            }
        }
        
        Label {
            text: driveSelector.displayText
            color: textColor
            font.pixelSize: 12
            elide: Text.ElideRight
            Layout.fillWidth: true
        }
        
        Image {
            source: "qrc:/icons/arrow-down.png"
            Layout.preferredWidth: 12
            Layout.preferredHeight: 12
            fillMode: Image.PreserveAspectFit
            
            ColorOverlay {
                anchors.fill: parent
                source: parent
                color: textColor
            }
            
            rotation: driveSelector.popup.visible ? 180 : 0
            
            Behavior on rotation {
                NumberAnimation { duration: 150 }
            }
        }
    }
    
    popup: Popup {
        y: driveSelector.height + 4
        width: Math.max(driveSelector.width, 250)
        height: Math.min(contentItem.implicitHeight, 300)
        
        background: Rectangle {
            color: surfaceColor
            border.color: borderColor
            border.width: 1
            radius: 6
            
            // Drop shadow effect
            Rectangle {
                anchors.fill: parent
                anchors.topMargin: 2
                anchors.leftMargin: 2
                color: Qt.rgba(0, 0, 0, 0.1)
                radius: parent.radius
                z: -1
            }
        }
        
        contentItem: ListView {
            id: driveListView
            clip: true
            model: driveModel
            currentIndex: driveSelector.highlightedIndex
            
            ScrollIndicator.vertical: ScrollIndicator {
                active: driveListView.contentHeight > driveListView.height
            }
            
            delegate: ItemDelegate {
                width: driveListView.width
                height: 48
                
                background: Rectangle {
                    color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                    radius: 4
                    
                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }
                }
                
                contentItem: RowLayout {
                    anchors.fill: parent
                    anchors.margins: 12
                    spacing: 12
                    
                    Image {
                        source: model.isRemovable ? "qrc:/icons/usb-drive.png" : "qrc:/icons/hard-drive.png"
                        Layout.preferredWidth: 20
                        Layout.preferredHeight: 20
                        fillMode: Image.PreserveAspectFit
                        
                        ColorOverlay {
                            anchors.fill: parent
                            source: parent
                            color: textColor
                        }
                    }
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 2
                        
                        Label {
                            text: model.displayName || model.path
                            color: textColor
                            font.pixelSize: 13
                            font.bold: true
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                        }
                        
                        RowLayout {
                            spacing: 8
                            
                            Label {
                                text: model.path
                                color: secondaryTextColor
                                font.pixelSize: 11
                                elide: Text.ElideMiddle
                                Layout.fillWidth: true
                            }
                            
                            Label {
                                text: model.availableSpaceFormatted
                                color: secondaryTextColor
                                font.pixelSize: 11
                            }
                        }
                    }
                    
                    // Status indicator
                    Rectangle {
                        width: 8
                        height: 8
                        radius: 4
                        color: model.isReady ? "#4CAF50" : "#F44336"
                        Layout.alignment: Qt.AlignVCenter
                    }
                }
                
                onClicked: {
                    driveSelector.currentIndex = index
                    driveSelector.popup.close()
                    driveSelector.driveSelected(model.path)
                }
            }
            
            // Empty state
            Label {
                anchors.centerIn: parent
                text: qsTr("No drives available")
                color: secondaryTextColor
                font.pixelSize: 12
                visible: driveListView.count === 0
            }
        }
    }
    
    // Refresh drives when popup opens
    onPopupVisibleChanged: {
        if (popup.visible) {
            refreshDrives()
        }
    }
    
    // Connections to drive watcher
    Connections {
        target: appCore.driveWatcher
        
        function onDrivesChanged() {
            refreshDrives()
        }
        
        function onDriveConnected(drivePath, driveInfo) {
            refreshDrives()
        }
        
        function onDriveDisconnected(drivePath) {
            refreshDrives()
        }
    }
    
    function refreshDrives() {
        driveModel.clear()
        
        var drives = appCore.driveWatcher.getDriveInfo()
        for (var i = 0; i < drives.length; i++) {
            var drive = drives[i]
            driveModel.append({
                path: drive.path,
                name: drive.name,
                displayName: drive.displayName,
                fileSystem: drive.fileSystem,
                totalSpace: drive.totalSpace,
                availableSpace: drive.availableSpace,
                totalSpaceFormatted: drive.totalSpaceFormatted,
                availableSpaceFormatted: drive.availableSpaceFormatted,
                isRemovable: drive.isRemovable,
                isReady: drive.isReady
            })
        }
    }
    
    Component.onCompleted: {
        refreshDrives()
    }
}
