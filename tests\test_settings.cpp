#include <QtTest/QtTest>
#include <QCoreApplication>
#include <QTemporaryDir>
#include <QStandardPaths>
#include "../include/settings.h"

class TestSettings : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Test cases
    void testDefaultValues();
    void testThemeSettings();
    void testBackupSettings();
    void testPathSettings();
    void testSettingsPersistence();
    void testImportExport();

private:
    Settings* settings;
    QTemporaryDir* tempDir;
};

void TestSettings::initTestCase()
{
    // Create temporary directory for test settings
    tempDir = new QTemporaryDir();
    QVERIFY(tempDir->isValid());
    
    // Override settings location for testing
    QCoreApplication::setOrganizationName("BackupProTest");
    QCoreApplication::setApplicationName("BackupProTest");
}

void TestSettings::cleanupTestCase()
{
    delete tempDir;
}

void TestSettings::init()
{
    settings = new Settings();
}

void TestSettings::cleanup()
{
    delete settings;
    settings = nullptr;
}

void TestSettings::testDefaultValues()
{
    // Test default theme
    QCOMPARE(settings->theme(), Settings::AppTheme::System);
    
    // Test default backup settings
    QVERIFY(settings->encryptionEnabled());
    QVERIFY(settings->incrementalBackupEnabled());
    QVERIFY(settings->verifyBackupIntegrity());
    QVERIFY(settings->showNotifications());
    
    // Test default compression
    QCOMPARE(settings->compressionLevel(), Settings::CompressionLevel::Medium);
    
    // Test default paths
    QVERIFY(!settings->defaultBackupPath().isEmpty());
    QVERIFY(!settings->tempPath().isEmpty());
}

void TestSettings::testThemeSettings()
{
    // Test setting theme to Light
    settings->setTheme(Settings::AppTheme::Light);
    QCOMPARE(settings->theme(), Settings::AppTheme::Light);
    
    // Test setting theme to Dark
    settings->setTheme(Settings::AppTheme::Dark);
    QCOMPARE(settings->theme(), Settings::AppTheme::Dark);
    
    // Test theme change signal
    QSignalSpy spy(settings, &Settings::themeChanged);
    settings->setTheme(Settings::AppTheme::System);
    QCOMPARE(spy.count(), 1);
    QCOMPARE(settings->theme(), Settings::AppTheme::System);
}

void TestSettings::testBackupSettings()
{
    // Test encryption setting
    settings->setEncryptionEnabled(false);
    QVERIFY(!settings->encryptionEnabled());
    
    QSignalSpy encryptionSpy(settings, &Settings::encryptionEnabledChanged);
    settings->setEncryptionEnabled(true);
    QCOMPARE(encryptionSpy.count(), 1);
    QVERIFY(settings->encryptionEnabled());
    
    // Test incremental backup setting
    settings->setIncrementalBackupEnabled(false);
    QVERIFY(!settings->incrementalBackupEnabled());
    
    // Test compression level
    settings->setCompressionLevel(Settings::CompressionLevel::High);
    QCOMPARE(settings->compressionLevel(), Settings::CompressionLevel::High);
    
    QSignalSpy compressionSpy(settings, &Settings::compressionLevelChanged);
    settings->setCompressionLevel(Settings::CompressionLevel::Low);
    QCOMPARE(compressionSpy.count(), 1);
    QCOMPARE(settings->compressionLevel(), Settings::CompressionLevel::Low);
}

void TestSettings::testPathSettings()
{
    QString testPath = "/test/backup/path";
    
    // Test default backup path
    settings->setDefaultBackupPath(testPath);
    QCOMPARE(settings->defaultBackupPath(), testPath);
    
    QSignalSpy pathSpy(settings, &Settings::defaultBackupPathChanged);
    QString newPath = "/new/test/path";
    settings->setDefaultBackupPath(newPath);
    QCOMPARE(pathSpy.count(), 1);
    QCOMPARE(settings->defaultBackupPath(), newPath);
    
    // Test temp path
    QString tempPath = "/test/temp/path";
    settings->setTempPath(tempPath);
    QCOMPARE(settings->tempPath(), tempPath);
}

void TestSettings::testSettingsPersistence()
{
    // Set some values
    settings->setTheme(Settings::AppTheme::Dark);
    settings->setEncryptionEnabled(false);
    settings->setCompressionLevel(Settings::CompressionLevel::High);
    settings->setDefaultBackupPath("/persistent/test/path");
    
    // Save settings
    settings->saveSettings();
    
    // Create new settings instance (should load saved values)
    Settings* newSettings = new Settings();
    
    // Verify values were persisted
    QCOMPARE(newSettings->theme(), Settings::AppTheme::Dark);
    QVERIFY(!newSettings->encryptionEnabled());
    QCOMPARE(newSettings->compressionLevel(), Settings::CompressionLevel::High);
    QCOMPARE(newSettings->defaultBackupPath(), QString("/persistent/test/path"));
    
    delete newSettings;
}

void TestSettings::testImportExport()
{
    // Set some test values
    settings->setTheme(Settings::AppTheme::Light);
    settings->setEncryptionEnabled(true);
    settings->setCompressionLevel(Settings::CompressionLevel::Low);
    
    // Export settings
    QString exportPath = tempDir->filePath("test_export.json");
    bool exportResult = settings->exportSettings(exportPath);
    QVERIFY(exportResult);
    QVERIFY(QFile::exists(exportPath));
    
    // Change settings
    settings->setTheme(Settings::AppTheme::Dark);
    settings->setEncryptionEnabled(false);
    settings->setCompressionLevel(Settings::CompressionLevel::High);
    
    // Import settings
    bool importResult = settings->importSettings(exportPath);
    QVERIFY(importResult);
    
    // Verify imported values
    QCOMPARE(settings->theme(), Settings::AppTheme::Light);
    QVERIFY(settings->encryptionEnabled());
    QCOMPARE(settings->compressionLevel(), Settings::CompressionLevel::Low);
}

QTEST_MAIN(TestSettings)
#include "test_settings.moc"
