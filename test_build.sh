#!/bin/bash

echo "========================================"
echo "BackupPro Build and Test Script"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Qt is available
echo "[1/6] Checking Qt installation..."
if command -v qmake >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Qt found${NC}"
    QT_VERSION=$(qmake --version | grep "Using Qt version" | cut -d' ' -f4)
    echo "  Qt version: $QT_VERSION"
else
    echo -e "${RED}ERROR: Qt/qmake not found in PATH${NC}"
    echo "Please install Qt and add it to your PATH"
    exit 1
fi

# Check if CMake is available
echo "[2/6] Checking CMake installation..."
if command -v cmake >/dev/null 2>&1; then
    echo -e "${GREEN}✓ CMake found${NC}"
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    echo "  CMake version: $CMAKE_VERSION"
    USE_CMAKE=1
else
    echo -e "${YELLOW}WARNING: CMake not found, will use QMake instead${NC}"
    USE_CMAKE=0
fi

# Create build directory
echo "[3/6] Setting up build directory..."
if [ -d "build" ]; then
    rm -rf build
fi
mkdir build
cd build

# Build the project
echo "[4/6] Building BackupPro..."
if [ $USE_CMAKE -eq 1 ]; then
    echo "Using CMake build system..."
    cmake ..
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: CMake configuration failed${NC}"
        cd ..
        exit 1
    fi
    
    cmake --build .
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: CMake build failed${NC}"
        cd ..
        exit 1
    fi
else
    echo "Using QMake build system..."
    qmake ../BackupPro.pro
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: QMake configuration failed${NC}"
        cd ..
        exit 1
    fi
    
    make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: Make build failed${NC}"
        cd ..
        exit 1
    fi
fi

# Check if executable was created
echo "[5/6] Verifying build output..."
if [ -f "BackupPro" ]; then
    echo -e "${GREEN}✓ BackupPro executable created successfully${NC}"
    
    # Check file permissions
    if [ -x "BackupPro" ]; then
        echo -e "${GREEN}✓ Executable permissions set${NC}"
    else
        echo -e "${YELLOW}Setting executable permissions...${NC}"
        chmod +x BackupPro
    fi
else
    echo -e "${RED}ERROR: BackupPro executable not found${NC}"
    echo "Build may have failed"
    cd ..
    exit 1
fi

# Test run the application (optional - comment out if running headless)
echo "[6/6] Testing application startup..."
echo "Starting BackupPro (will attempt to run for 3 seconds)..."

# Check if we have a display (for GUI testing)
if [ -n "$DISPLAY" ] || [ "$(uname)" = "Darwin" ]; then
    echo "Display detected, testing GUI startup..."
    timeout 3s ./BackupPro &
    APP_PID=$!
    sleep 1
    
    # Check if process is still running
    if kill -0 $APP_PID 2>/dev/null; then
        echo -e "${GREEN}✓ Application started successfully${NC}"
        kill $APP_PID 2>/dev/null
        wait $APP_PID 2>/dev/null
    else
        echo -e "${YELLOW}Application may have crashed or exited immediately${NC}"
        echo "Check console output for errors"
    fi
else
    echo -e "${YELLOW}No display detected, skipping GUI test${NC}"
    echo "Run './BackupPro' manually to test the application"
fi

cd ..

echo
echo "========================================"
echo "Build and Test Complete!"
echo "========================================"
echo
echo "To run the application manually:"
echo "  cd build"
echo "  ./BackupPro"
echo
echo "Check TESTING_GUIDE.md for detailed testing instructions."
echo

# Check for common Qt libraries
echo "System Information:"
echo "  OS: $(uname -s)"
echo "  Architecture: $(uname -m)"

if command -v ldd >/dev/null 2>&1; then
    echo
    echo "Checking Qt library dependencies..."
    ldd build/BackupPro | grep -i qt | head -5
elif command -v otool >/dev/null 2>&1; then
    echo
    echo "Checking Qt library dependencies..."
    otool -L build/BackupPro | grep -i qt | head -5
fi
