# BackupPro Testing Guide

## Prerequisites

### Required Software
1. **Qt 6.5 or later** with the following modules:
   - Qt Core, Gui, Quick, Qml, Widgets
   - Qt Network, Concurrent
   - Qt Quick Controls 2

2. **C++ Compiler**:
   - Windows: MSVC 2019+ or MinGW
   - macOS: Xcode Command Line Tools
   - Linux: GCC 9+ or Clang 10+

3. **Build Tools**:
   - CMake 3.21+ (recommended)
   - Or QMake (alternative)

### Installation Commands

#### Windows (using Qt Online Installer)
1. Download Qt Online Installer from qt.io
2. Install Qt 6.5+ with Qt Quick Controls 2
3. Add Qt bin directory to PATH

#### macOS (using Homebrew)
```bash
brew install qt@6
export PATH="/opt/homebrew/opt/qt@6/bin:$PATH"
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install qt6-base-dev qt6-declarative-dev qt6-quick-controls2-dev
sudo apt install cmake build-essential
```

## Step 2: Build the Application

### Option A: Using CMake (Recommended)

1. **Create build directory**:
```bash
mkdir build
cd build
```

2. **Configure the project**:
```bash
cmake ..
```

3. **Build the application**:
```bash
cmake --build .
```

### Option B: Using QMake

1. **Generate Makefile**:
```bash
qmake BackupPro.pro
```

2. **Build the application**:
```bash
# Windows (MinGW)
mingw32-make

# Windows (MSVC)
nmake

# macOS/Linux
make
```

## Step 3: Run the Application

### From Build Directory
```bash
# Windows
./BackupPro.exe

# macOS/Linux
./BackupPro
```

### Expected Startup Behavior
- Application window should open with navigation sidebar
- Default page should be "Backup"
- Theme should follow system preference (dark/light)
- No error messages in console

## Step 4: Feature Testing

### 4.1 Navigation Testing
- [ ] Click each navigation button (Backup, Restore, Schedule, History, Settings)
- [ ] Verify page transitions work smoothly
- [ ] Check that active page is highlighted in navigation

### 4.2 Backup Page Testing
- [ ] Click "Add Files" button - file dialog should open
- [ ] Click "Add Folders" button - folder dialog should open
- [ ] Select destination using "Browse" button
- [ ] Test drive selector dropdown (should show available drives)
- [ ] Toggle backup options (Incremental, Encryption, Compression)
- [ ] Click "Start Backup" button (should show progress indicator)

### 4.3 Restore Page Testing
- [ ] Browse for backup source
- [ ] Select restore destination
- [ ] Toggle restore options
- [ ] Test "Verify Backup" functionality

### 4.4 Schedule Page Testing
- [ ] View existing schedules (sample data should be visible)
- [ ] Test enable/disable toggle for schedules
- [ ] Click "New Schedule" button

### 4.5 History Page Testing
- [ ] View backup history (sample data should be visible)
- [ ] Test "Refresh" button
- [ ] Check status indicators (success/failed)

### 4.6 Settings Page Testing
- [ ] Change theme (System/Light/Dark)
- [ ] Modify default backup path
- [ ] Toggle various settings checkboxes
- [ ] Click "Save Settings" button

### 4.7 Theme Testing
- [ ] Switch between Light/Dark/System themes
- [ ] Verify all UI elements adapt to theme changes
- [ ] Check color consistency across all pages

## Step 5: Console Output Testing

Monitor console output for:
- [ ] No Qt warnings or errors
- [ ] Proper QML component loading
- [ ] Backend initialization messages
- [ ] Signal/slot connections working

## Step 6: Performance Testing

- [ ] Application starts within 3 seconds
- [ ] Page transitions are smooth (no lag)
- [ ] Memory usage remains stable
- [ ] No memory leaks during navigation

## Step 7: Cross-Platform Testing

### Windows Specific
- [ ] Test with different Windows versions (10, 11)
- [ ] Verify drive detection works
- [ ] Test with different screen DPI settings

### macOS Specific
- [ ] Test on different macOS versions
- [ ] Verify native look and feel
- [ ] Test with Retina displays

### Linux Specific
- [ ] Test on different distributions
- [ ] Verify theme integration with desktop environment

## Troubleshooting Common Issues

### Build Issues

**Qt not found**:
```bash
# Set Qt path manually
export Qt6_DIR=/path/to/qt6/lib/cmake/Qt6
cmake ..
```

**Missing modules**:
- Ensure Qt Quick Controls 2 is installed
- Check that all required Qt modules are available

### Runtime Issues

**QML import errors**:
- Verify QML files are in correct locations
- Check resource file (qml.qrc) includes all QML files

**Missing icons**:
- Icons are referenced but not included in resources
- Application will show placeholder icons

**Backend not responding**:
- Check console for C++ errors
- Verify signal/slot connections

### Performance Issues

**Slow startup**:
- Check for large resource files
- Verify Qt installation is complete

**Memory leaks**:
- Monitor with Qt Creator's profiler
- Check for proper object cleanup

## Expected Test Results

### Successful Test Indicators
- ✅ Application launches without errors
- ✅ All pages load and display correctly
- ✅ Navigation works smoothly
- ✅ UI adapts to theme changes
- ✅ File/folder dialogs open correctly
- ✅ No console errors or warnings

### Known Limitations (Current Implementation)
- 🔄 Backend functionality is placeholder (console.log outputs)
- 🔄 File operations don't perform actual backup/restore
- 🔄 Drive detection shows sample data
- 🔄 Settings don't persist between sessions
- 🔄 Progress indicators show demo animations

## Next Steps for Full Implementation

1. **Connect QML to C++ backend**:
   - Implement actual file operations
   - Connect progress signals
   - Add error handling

2. **Add unit tests**:
   - Create test cases for each C++ class
   - Test QML components individually

3. **Add integration tests**:
   - Test complete backup/restore workflows
   - Verify cross-platform compatibility

4. **Performance optimization**:
   - Profile memory usage
   - Optimize large file operations
   - Add background processing
