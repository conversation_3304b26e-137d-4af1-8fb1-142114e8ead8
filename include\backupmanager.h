#ifndef BACKUPMANAGER_H
#define BACKUPMANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantList>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QTimer>
#include <memory>

class FileOperations;
class Encryption;

/**
 * @brief Backup record structure
 */
struct BackupRecord {
    QString id;
    QString sourcePath;
    QString destinationPath;
    QDateTime timestamp;
    qint64 totalSize;
    int fileCount;
    bool encrypted;
    bool successful;
    QString errorMessage;
};

/**
 * @brief The BackupManager class handles all backup operations
 * 
 * This class manages backup creation, incremental backups, backup verification,
 * and maintains backup history and metadata.
 */
class BackupManager : public QObject
{
    Q_OBJECT

public:
    explicit BackupManager(QObject *parent = nullptr);
    ~BackupManager();

    /**
     * @brief Start a backup operation
     * @param sourcePath Source directory to backup
     * @param destinationPath Destination directory for backup
     * @param encrypted Whether to encrypt the backup
     * @param incremental Whether to perform incremental backup
     */
    void startBackup(const QString &sourcePath, const QString &destinationPath, 
                    bool encrypted = false, bool incremental = true);

    /**
     * @brief Stop the current backup operation
     */
    void stopBackup();

    /**
     * @brief Check if backup is currently in progress
     * @return true if backup is running
     */
    bool isBackupInProgress() const;

    /**
     * @brief Get backup history
     * @return List of backup records
     */
    QVariantList getBackupHistory() const;

    /**
     * @brief Get backup record by ID
     * @param backupId Backup identifier
     * @return Backup record or empty record if not found
     */
    BackupRecord getBackupRecord(const QString &backupId) const;

    /**
     * @brief Verify backup integrity
     * @param backupPath Path to backup to verify
     * @return true if backup is valid
     */
    bool verifyBackup(const QString &backupPath);

    /**
     * @brief Delete a backup
     * @param backupId Backup identifier
     * @return true if deletion successful
     */
    bool deleteBackup(const QString &backupId);

    /**
     * @brief Get estimated backup size
     * @param sourcePath Source directory
     * @return Estimated size in bytes
     */
    qint64 getEstimatedBackupSize(const QString &sourcePath);

public slots:
    /**
     * @brief Schedule automatic backup
     * @param sourcePath Source directory
     * @param destinationPath Destination directory
     * @param intervalHours Backup interval in hours
     */
    void scheduleBackup(const QString &sourcePath, const QString &destinationPath, int intervalHours);

signals:
    /**
     * @brief Emitted when backup progress changes
     * @param progress Progress percentage (0-100)
     */
    void progressChanged(int progress);

    /**
     * @brief Emitted when backup status changes
     * @param status Current status message
     */
    void statusChanged(const QString &status);

    /**
     * @brief Emitted when backup completes
     * @param success Whether backup was successful
     * @param message Result message
     */
    void backupCompleted(bool success, const QString &message);

    /**
     * @brief Emitted when backup starts
     * @param sourcePath Source directory
     * @param destinationPath Destination directory
     */
    void backupStarted(const QString &sourcePath, const QString &destinationPath);

private slots:
    void onFileProgress(const QString &fileName, qint64 bytesProcessed, qint64 totalBytes);
    void onBackupThreadFinished();

private:
    void performBackup();
    void saveBackupRecord(const BackupRecord &record);
    void loadBackupHistory();
    QString generateBackupId() const;
    QString createBackupMetadata(const BackupRecord &record) const;
    bool createIncrementalBackup(const QString &sourcePath, const QString &destinationPath);
    bool createFullBackup(const QString &sourcePath, const QString &destinationPath);

    std::unique_ptr<FileOperations> m_fileOperations;
    std::unique_ptr<Encryption> m_encryption;
    QThread *m_backupThread;
    
    // Current backup state
    QString m_currentSourcePath;
    QString m_currentDestinationPath;
    bool m_currentEncrypted;
    bool m_currentIncremental;
    bool m_isBackupInProgress;
    bool m_stopRequested;
    
    // Progress tracking
    int m_currentProgress;
    qint64 m_totalBytesToProcess;
    qint64 m_bytesProcessed;
    
    // Backup history
    QList<BackupRecord> m_backupHistory;
    mutable QMutex m_historyMutex;
    
    // Scheduled backup
    QTimer *m_scheduleTimer;
};

Q_DECLARE_METATYPE(BackupRecord)

#endif // BACKUPMANAGER_H
