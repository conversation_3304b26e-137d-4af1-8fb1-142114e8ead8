#include "encryption.h"
#include <QFile>
#include <QRandomGenerator>
#include <QLoggingCategory>
#include <QRegularExpression>

Q_LOGGING_CATEGORY(encryption, "backuppro.encryption")

const QByteArray Encryption::FILE_MAGIC = "BKPRO";

Encryption::Encryption(QObject *parent)
    : QObject(parent)
    , m_algorithm(EncryptionAlgorithm::AES256)
{
    qCDebug(encryption) << "Encryption created";
}

Encryption::~Encryption()
{
    qCDebug(encryption) << "Encryption destroyed";
}

void Encryption::setAlgorithm(EncryptionAlgorithm algorithm)
{
    m_algorithm = algorithm;
    qCDebug(encryption) << "Encryption algorithm set to:" << static_cast<int>(algorithm);
}

EncryptionResult Encryption::encryptData(const QByteArray &data, const QString &password)
{
    EncryptionResult result;
    result.success = false;
    
    if (data.isEmpty() || password.isEmpty()) {
        result.errorMessage = "Data or password is empty";
        return result;
    }
    
    try {
        // Generate salt and IV
        result.salt = generateSalt(SALT_LENGTH);
        result.iv = generateIV(IV_LENGTH);
        
        // Derive key from password
        QByteArray key = deriveKey(password, result.salt, PBKDF2_ITERATIONS, KEY_LENGTH);
        
        // Encrypt data
        EncryptionResult encResult = encryptAES(data, key, result.iv);
        if (!encResult.success) {
            result.errorMessage = encResult.errorMessage;
            return result;
        }
        
        result.data = encResult.data;
        result.success = true;
        
        // Securely wipe the key
        secureWipe(key);
        
    } catch (const std::exception &e) {
        result.errorMessage = QString("Encryption failed: %1").arg(e.what());
        qCCritical(encryption) << "Encryption exception:" << e.what();
    }
    
    return result;
}

EncryptionResult Encryption::decryptData(const QByteArray &encryptedData, const QString &password,
                                        const QByteArray &salt, const QByteArray &iv)
{
    EncryptionResult result;
    result.success = false;
    
    if (encryptedData.isEmpty() || password.isEmpty() || salt.isEmpty() || iv.isEmpty()) {
        result.errorMessage = "Invalid input parameters";
        return result;
    }
    
    try {
        // Derive key from password
        QByteArray key = deriveKey(password, salt, PBKDF2_ITERATIONS, KEY_LENGTH);
        
        // Decrypt data
        EncryptionResult decResult = decryptAES(encryptedData, key, iv);
        if (!decResult.success) {
            result.errorMessage = decResult.errorMessage;
            return result;
        }
        
        result.data = decResult.data;
        result.success = true;
        
        // Securely wipe the key
        secureWipe(key);
        
    } catch (const std::exception &e) {
        result.errorMessage = QString("Decryption failed: %1").arg(e.what());
        qCCritical(encryption) << "Decryption exception:" << e.what();
    }
    
    return result;
}

bool Encryption::encryptFile(const QString &inputFilePath, const QString &outputFilePath, 
                            const QString &password)
{
    emit statusChanged("Starting file encryption...");
    
    QFile inputFile(inputFilePath);
    if (!inputFile.open(QIODevice::ReadOnly)) {
        emit operationCompleted(false, "Failed to open input file");
        return false;
    }
    
    QFile outputFile(outputFilePath);
    if (!outputFile.open(QIODevice::WriteOnly)) {
        emit operationCompleted(false, "Failed to create output file");
        return false;
    }
    
    try {
        // Generate salt and IV
        QByteArray salt = generateSalt(SALT_LENGTH);
        QByteArray iv = generateIV(IV_LENGTH);
        
        // Derive key
        QByteArray key = deriveKey(password, salt, PBKDF2_ITERATIONS, KEY_LENGTH);
        
        // Write file header
        QByteArray header = createFileHeader(salt, iv, m_algorithm);
        outputFile.write(header);
        
        // Process file encryption
        bool success = processFileEncryption(inputFilePath, outputFilePath, key, iv, true);
        
        // Securely wipe the key
        secureWipe(key);
        
        if (success) {
            emit operationCompleted(true, "File encrypted successfully");
        } else {
            emit operationCompleted(false, "File encryption failed");
        }
        
        return success;
        
    } catch (const std::exception &e) {
        emit operationCompleted(false, QString("Encryption error: %1").arg(e.what()));
        return false;
    }
}

bool Encryption::decryptFile(const QString &inputFilePath, const QString &outputFilePath, 
                            const QString &password)
{
    emit statusChanged("Starting file decryption...");
    
    QFile inputFile(inputFilePath);
    if (!inputFile.open(QIODevice::ReadOnly)) {
        emit operationCompleted(false, "Failed to open encrypted file");
        return false;
    }
    
    try {
        // Read and parse header
        QByteArray header = inputFile.read(FILE_MAGIC.length() + SALT_LENGTH + IV_LENGTH + sizeof(int));
        
        QByteArray salt, iv;
        EncryptionAlgorithm algorithm;
        
        if (!parseFileHeader(header, salt, iv, algorithm)) {
            emit operationCompleted(false, "Invalid encrypted file format");
            return false;
        }
        
        // Derive key
        QByteArray key = deriveKey(password, salt, PBKDF2_ITERATIONS, KEY_LENGTH);
        
        // Process file decryption
        bool success = processFileEncryption(inputFilePath, outputFilePath, key, iv, false);
        
        // Securely wipe the key
        secureWipe(key);
        
        if (success) {
            emit operationCompleted(true, "File decrypted successfully");
        } else {
            emit operationCompleted(false, "File decryption failed");
        }
        
        return success;
        
    } catch (const std::exception &e) {
        emit operationCompleted(false, QString("Decryption error: %1").arg(e.what()));
        return false;
    }
}

QString Encryption::generatePassword(int length, bool includeSymbols)
{
    const QString lowercase = "abcdefghijklmnopqrstuvwxyz";
    const QString uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const QString numbers = "0123456789";
    const QString symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    
    QString charset = lowercase + uppercase + numbers;
    if (includeSymbols) {
        charset += symbols;
    }
    
    QString password;
    for (int i = 0; i < length; ++i) {
        int index = QRandomGenerator::global()->bounded(charset.length());
        password.append(charset.at(index));
    }
    
    return password;
}

QByteArray Encryption::generateSalt(int length)
{
    QByteArray salt;
    salt.resize(length);
    
    for (int i = 0; i < length; ++i) {
        salt[i] = static_cast<char>(QRandomGenerator::global()->bounded(256));
    }
    
    return salt;
}

QByteArray Encryption::generateIV(int length)
{
    return generateSalt(length); // Same implementation as salt generation
}

QByteArray Encryption::deriveKey(const QString &password, const QByteArray &salt, 
                                int iterations, int keyLength)
{
    // Simplified PBKDF2 implementation using QCryptographicHash
    QByteArray passwordBytes = password.toUtf8();
    QByteArray key;
    
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(passwordBytes);
    hash.addData(salt);
    
    QByteArray result = hash.result();
    
    // Perform iterations
    for (int i = 1; i < iterations; ++i) {
        hash.reset();
        hash.addData(result);
        result = hash.result();
    }
    
    // Truncate or extend to desired key length
    if (result.length() >= keyLength) {
        return result.left(keyLength);
    } else {
        // Extend by repeating the hash
        key = result;
        while (key.length() < keyLength) {
            hash.reset();
            hash.addData(key);
            key.append(hash.result());
        }
        return key.left(keyLength);
    }
}

QString Encryption::calculateHash(const QByteArray &data, QCryptographicHash::Algorithm algorithm)
{
    QCryptographicHash hash(algorithm);
    hash.addData(data);
    return hash.result().toHex();
}

QString Encryption::calculateFileHash(const QString &filePath, QCryptographicHash::Algorithm algorithm)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }
    
    QCryptographicHash hash(algorithm);
    
    const int bufferSize = 64 * 1024; // 64KB buffer
    while (!file.atEnd()) {
        QByteArray data = file.read(bufferSize);
        hash.addData(data);
    }
    
    return hash.result().toHex();
}

int Encryption::checkPasswordStrength(const QString &password)
{
    int score = 0;
    
    // Length check
    if (password.length() >= 8) score += 20;
    if (password.length() >= 12) score += 10;
    if (password.length() >= 16) score += 10;
    
    // Character variety checks
    if (password.contains(QRegularExpression("[a-z]"))) score += 15;
    if (password.contains(QRegularExpression("[A-Z]"))) score += 15;
    if (password.contains(QRegularExpression("[0-9]"))) score += 15;
    if (password.contains(QRegularExpression("[^a-zA-Z0-9]"))) score += 15;
    
    return qMin(score, 100);
}

bool Encryption::isPasswordValid(const QString &password)
{
    return password.length() >= 8 && checkPasswordStrength(password) >= 60;
}

void Encryption::secureWipe(QByteArray &data)
{
    // Overwrite with random data multiple times
    for (int pass = 0; pass < 3; ++pass) {
        for (int i = 0; i < data.size(); ++i) {
            data[i] = static_cast<char>(QRandomGenerator::global()->bounded(256));
        }
    }
    
    // Clear the array
    data.clear();
}

QByteArray Encryption::createEncryptedMetadata(const QByteArray &metadata, const QString &password)
{
    EncryptionResult result = encryptData(metadata, password);
    if (result.success) {
        // Combine salt, IV, and encrypted data
        QByteArray combined;
        combined.append(result.salt);
        combined.append(result.iv);
        combined.append(result.data);
        return combined;
    }
    
    return QByteArray();
}

QByteArray Encryption::readEncryptedMetadata(const QByteArray &encryptedMetadata, const QString &password)
{
    if (encryptedMetadata.length() < SALT_LENGTH + IV_LENGTH) {
        return QByteArray();
    }
    
    QByteArray salt = encryptedMetadata.left(SALT_LENGTH);
    QByteArray iv = encryptedMetadata.mid(SALT_LENGTH, IV_LENGTH);
    QByteArray data = encryptedMetadata.mid(SALT_LENGTH + IV_LENGTH);
    
    EncryptionResult result = decryptData(data, password, salt, iv);
    return result.success ? result.data : QByteArray();
}

bool Encryption::testEncryption(const QString &password)
{
    const QByteArray testData = "BackupPro encryption test data";
    
    EncryptionResult encResult = encryptData(testData, password);
    if (!encResult.success) {
        return false;
    }
    
    EncryptionResult decResult = decryptData(encResult.data, password, encResult.salt, encResult.iv);
    if (!decResult.success) {
        return false;
    }
    
    return decResult.data == testData;
}

// Simplified AES encryption/decryption (placeholder implementation)
EncryptionResult Encryption::encryptAES(const QByteArray &data, const QByteArray &key, const QByteArray &iv)
{
    EncryptionResult result;
    
    // This is a simplified placeholder implementation
    // In a real application, you would use a proper cryptographic library like OpenSSL
    
    QByteArray encrypted = data;
    
    // Simple XOR encryption for demonstration (NOT SECURE - use proper AES implementation)
    for (int i = 0; i < encrypted.size(); ++i) {
        encrypted[i] = encrypted[i] ^ key[i % key.size()] ^ iv[i % iv.size()];
    }
    
    result.data = encrypted;
    result.success = true;
    
    return result;
}

EncryptionResult Encryption::decryptAES(const QByteArray &encryptedData, const QByteArray &key, const QByteArray &iv)
{
    // For this simple XOR implementation, decryption is the same as encryption
    return encryptAES(encryptedData, key, iv);
}

bool Encryption::processFileEncryption(const QString &inputPath, const QString &outputPath, 
                                      const QByteArray &key, const QByteArray &iv, bool encrypt)
{
    Q_UNUSED(inputPath)
    Q_UNUSED(outputPath)
    Q_UNUSED(key)
    Q_UNUSED(iv)
    Q_UNUSED(encrypt)
    
    // Placeholder implementation
    // In a real application, this would process the file in chunks
    emit progressChanged(100);
    return true;
}

QByteArray Encryption::createFileHeader(const QByteArray &salt, const QByteArray &iv, 
                                       EncryptionAlgorithm algorithm)
{
    QByteArray header;
    header.append(FILE_MAGIC);
    header.append(salt);
    header.append(iv);
    
    // Add algorithm identifier
    int algoId = static_cast<int>(algorithm);
    header.append(reinterpret_cast<const char*>(&algoId), sizeof(algoId));
    
    return header;
}

bool Encryption::parseFileHeader(const QByteArray &header, QByteArray &salt, QByteArray &iv, 
                                EncryptionAlgorithm &algorithm)
{
    if (header.length() < FILE_MAGIC.length() + SALT_LENGTH + IV_LENGTH + sizeof(int)) {
        return false;
    }
    
    // Check magic bytes
    if (!header.startsWith(FILE_MAGIC)) {
        return false;
    }
    
    int offset = FILE_MAGIC.length();
    
    salt = header.mid(offset, SALT_LENGTH);
    offset += SALT_LENGTH;
    
    iv = header.mid(offset, IV_LENGTH);
    offset += IV_LENGTH;
    
    int algoId;
    memcpy(&algoId, header.data() + offset, sizeof(algoId));
    algorithm = static_cast<EncryptionAlgorithm>(algoId);
    
    return true;
}
