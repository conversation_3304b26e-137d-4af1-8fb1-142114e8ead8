import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import BackupPro 1.0

Page {
    id: settingsPage
    
    property bool isDarkTheme: parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color successColor: "#4CAF50"
    
    background: Rectangle {
        color: backgroundColor
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 24
        
        ColumnLayout {
            width: parent.width
            spacing: 24
            
            // Page header
            Label {
                text: qsTr("Settings")
                font.pixelSize: 28
                font.bold: true
                color: textColor
            }
            
            // General Settings
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("General")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                GridLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    columns: 2
                    columnSpacing: 16
                    rowSpacing: 12
                    
                    Label {
                        text: qsTr("Theme:")
                        color: textColor
                        Layout.alignment: Qt.AlignVCenter
                    }
                    
                    ComboBox {
                        id: themeComboBox
                        Layout.fillWidth: true
                        model: [qsTr("System"), qsTr("Light"), qsTr("Dark")]
                        currentIndex: 0 // appCore.settings.theme
                        
                        background: Rectangle {
                            color: surfaceColor
                            border.color: parent.activeFocus ? primaryColor : borderColor
                            border.width: parent.activeFocus ? 2 : 1
                            radius: 4
                        }
                        
                        contentItem: Label {
                            text: parent.displayText
                            color: textColor
                            leftPadding: 12
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                    
                    Label {
                        text: qsTr("Language:")
                        color: textColor
                        Layout.alignment: Qt.AlignVCenter
                    }
                    
                    ComboBox {
                        id: languageComboBox
                        Layout.fillWidth: true
                        model: [qsTr("English"), qsTr("Spanish"), qsTr("French"), qsTr("German")]
                        currentIndex: 0
                        
                        background: Rectangle {
                            color: surfaceColor
                            border.color: parent.activeFocus ? primaryColor : borderColor
                            border.width: parent.activeFocus ? 2 : 1
                            radius: 4
                        }
                        
                        contentItem: Label {
                            text: parent.displayText
                            color: textColor
                            leftPadding: 12
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                    
                    Label {
                        text: qsTr("Default Backup Path:")
                        color: textColor
                        Layout.alignment: Qt.AlignVCenter
                    }
                    
                    RowLayout {
                        Layout.fillWidth: true
                        
                        TextField {
                            id: defaultPathField
                            Layout.fillWidth: true
                            text: "C:/Users/<USER>/Documents/Backups" // appCore.settings.defaultBackupPath
                            color: textColor
                            
                            background: Rectangle {
                                color: backgroundColor
                                border.color: parent.activeFocus ? primaryColor : borderColor
                                border.width: parent.activeFocus ? 2 : 1
                                radius: 4
                            }
                        }
                        
                        Button {
                            text: qsTr("Browse")
                            
                            background: Rectangle {
                                color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                border.color: primaryColor
                                border.width: 1
                                radius: 4
                            }
                            
                            contentItem: Label {
                                text: parent.text
                                color: primaryColor
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }
                }
            }
            
            // Backup Settings
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Backup")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 12
                    
                    CheckBox {
                        text: qsTr("Enable encryption by default")
                        checked: true // appCore.settings.encryptionEnabled
                        
                        indicator: Rectangle {
                            implicitWidth: 20
                            implicitHeight: 20
                            x: parent.leftPadding
                            y: parent.height / 2 - height / 2
                            radius: 3
                            border.color: parent.checked ? primaryColor : borderColor
                            border.width: parent.checked ? 2 : 1
                            color: parent.checked ? primaryColor : "transparent"
                            
                            Image {
                                width: 12
                                height: 12
                                anchors.centerIn: parent
                                source: "qrc:/icons/check.png"
                                visible: parent.parent.checked
                                
                                ColorOverlay {
                                    anchors.fill: parent
                                    source: parent
                                    color: "white"
                                }
                            }
                        }
                        
                        contentItem: Label {
                            text: parent.text
                            color: textColor
                            leftPadding: parent.indicator.width + parent.spacing
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                    
                    CheckBox {
                        text: qsTr("Enable incremental backups by default")
                        checked: true // appCore.settings.incrementalBackupEnabled
                        
                        indicator: Rectangle {
                            implicitWidth: 20
                            implicitHeight: 20
                            x: parent.leftPadding
                            y: parent.height / 2 - height / 2
                            radius: 3
                            border.color: parent.checked ? primaryColor : borderColor
                            border.width: parent.checked ? 2 : 1
                            color: parent.checked ? primaryColor : "transparent"
                            
                            Image {
                                width: 12
                                height: 12
                                anchors.centerIn: parent
                                source: "qrc:/icons/check.png"
                                visible: parent.parent.checked
                                
                                ColorOverlay {
                                    anchors.fill: parent
                                    source: parent
                                    color: "white"
                                }
                            }
                        }
                        
                        contentItem: Label {
                            text: parent.text
                            color: textColor
                            leftPadding: parent.indicator.width + parent.spacing
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                    
                    CheckBox {
                        text: qsTr("Verify backup integrity")
                        checked: true // appCore.settings.verifyBackupIntegrity
                        
                        indicator: Rectangle {
                            implicitWidth: 20
                            implicitHeight: 20
                            x: parent.leftPadding
                            y: parent.height / 2 - height / 2
                            radius: 3
                            border.color: parent.checked ? primaryColor : borderColor
                            border.width: parent.checked ? 2 : 1
                            color: parent.checked ? primaryColor : "transparent"
                            
                            Image {
                                width: 12
                                height: 12
                                anchors.centerIn: parent
                                source: "qrc:/icons/check.png"
                                visible: parent.parent.checked
                                
                                ColorOverlay {
                                    anchors.fill: parent
                                    source: parent
                                    color: "white"
                                }
                            }
                        }
                        
                        contentItem: Label {
                            text: parent.text
                            color: textColor
                            leftPadding: parent.indicator.width + parent.spacing
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                    
                    CheckBox {
                        text: qsTr("Show notifications")
                        checked: true // appCore.settings.showNotifications
                        
                        indicator: Rectangle {
                            implicitWidth: 20
                            implicitHeight: 20
                            x: parent.leftPadding
                            y: parent.height / 2 - height / 2
                            radius: 3
                            border.color: parent.checked ? primaryColor : borderColor
                            border.width: parent.checked ? 2 : 1
                            color: parent.checked ? primaryColor : "transparent"
                            
                            Image {
                                width: 12
                                height: 12
                                anchors.centerIn: parent
                                source: "qrc:/icons/check.png"
                                visible: parent.parent.checked
                                
                                ColorOverlay {
                                    anchors.fill: parent
                                    source: parent
                                    color: "white"
                                }
                            }
                        }
                        
                        contentItem: Label {
                            text: parent.text
                            color: textColor
                            leftPadding: parent.indicator.width + parent.spacing
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }
            }
            
            // Action buttons
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                Button {
                    text: qsTr("Save Settings")
                    Layout.preferredWidth: 150
                    
                    background: Rectangle {
                        color: parent.hovered ? Qt.darker(successColor, 1.1) : successColor
                        radius: 6
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        // TODO: Save settings
                        console.log("Saving settings")
                    }
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Reset to Defaults")
                    
                    background: Rectangle {
                        color: parent.hovered ? Qt.rgba(secondaryTextColor.r, secondaryTextColor.g, secondaryTextColor.b, 0.1) : "transparent"
                        border.color: secondaryTextColor
                        border.width: 1
                        radius: 6
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: secondaryTextColor
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        // TODO: Reset settings to defaults
                        console.log("Resetting settings to defaults")
                    }
                }
            }
            
            Item { Layout.fillHeight: true }
        }
    }
}
