#ifndef ENCRYPTION_H
#define ENCRYPTION_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QCryptographicHash>
#include <QRandomGenerator>

/**
 * @brief Encryption algorithm types
 */
enum class EncryptionAlgorithm {
    AES256,
    AES128,
    ChaCha20
};

/**
 * @brief Encryption result structure
 */
struct EncryptionResult {
    bool success;
    QByteArray data;
    QString errorMessage;
    QByteArray salt;
    QByteArray iv;
};

/**
 * @brief The Encryption class provides data encryption and decryption
 * 
 * This class handles file and data encryption using various algorithms,
 * key derivation, secure random generation, and provides utilities for
 * secure backup storage.
 */
class Encryption : public QObject
{
    Q_OBJECT

public:
    explicit Encryption(QObject *parent = nullptr);
    ~Encryption();

    /**
     * @brief Set encryption algorithm
     * @param algorithm Algorithm to use
     */
    void setAlgorithm(EncryptionAlgorithm algorithm);

    /**
     * @brief Get current encryption algorithm
     * @return Current algorithm
     */
    EncryptionAlgorithm algorithm() const { return m_algorithm; }

    /**
     * @brief Encrypt data with password
     * @param data Data to encrypt
     * @param password Encryption password
     * @return EncryptionResult with encrypted data
     */
    EncryptionResult encryptData(const QByteArray &data, const QString &password);

    /**
     * @brief Decrypt data with password
     * @param encryptedData Encrypted data
     * @param password Decryption password
     * @param salt Salt used during encryption
     * @param iv Initialization vector used during encryption
     * @return EncryptionResult with decrypted data
     */
    EncryptionResult decryptData(const QByteArray &encryptedData, const QString &password,
                                const QByteArray &salt, const QByteArray &iv);

    /**
     * @brief Encrypt file
     * @param inputFilePath Path to file to encrypt
     * @param outputFilePath Path for encrypted file
     * @param password Encryption password
     * @return true if encryption successful
     */
    bool encryptFile(const QString &inputFilePath, const QString &outputFilePath, 
                    const QString &password);

    /**
     * @brief Decrypt file
     * @param inputFilePath Path to encrypted file
     * @param outputFilePath Path for decrypted file
     * @param password Decryption password
     * @return true if decryption successful
     */
    bool decryptFile(const QString &inputFilePath, const QString &outputFilePath, 
                    const QString &password);

    /**
     * @brief Generate secure random password
     * @param length Password length
     * @param includeSymbols Whether to include symbols
     * @return Generated password
     */
    QString generatePassword(int length = 32, bool includeSymbols = true);

    /**
     * @brief Generate secure random salt
     * @param length Salt length in bytes
     * @return Generated salt
     */
    QByteArray generateSalt(int length = 32);

    /**
     * @brief Generate secure random IV
     * @param length IV length in bytes
     * @return Generated IV
     */
    QByteArray generateIV(int length = 16);

    /**
     * @brief Derive key from password using PBKDF2
     * @param password Password to derive from
     * @param salt Salt for key derivation
     * @param iterations Number of iterations
     * @param keyLength Desired key length
     * @return Derived key
     */
    QByteArray deriveKey(const QString &password, const QByteArray &salt, 
                        int iterations = 100000, int keyLength = 32);

    /**
     * @brief Calculate hash of data
     * @param data Data to hash
     * @param algorithm Hash algorithm
     * @return Hash as hex string
     */
    QString calculateHash(const QByteArray &data, 
                         QCryptographicHash::Algorithm algorithm = QCryptographicHash::Sha256);

    /**
     * @brief Calculate hash of file
     * @param filePath Path to file
     * @param algorithm Hash algorithm
     * @return Hash as hex string
     */
    QString calculateFileHash(const QString &filePath,
                             QCryptographicHash::Algorithm algorithm = QCryptographicHash::Sha256);

    /**
     * @brief Verify password strength
     * @param password Password to check
     * @return Strength score (0-100)
     */
    int checkPasswordStrength(const QString &password);

    /**
     * @brief Check if password meets minimum requirements
     * @param password Password to check
     * @return true if password is acceptable
     */
    bool isPasswordValid(const QString &password);

    /**
     * @brief Securely wipe memory containing sensitive data
     * @param data Data to wipe
     */
    static void secureWipe(QByteArray &data);

    /**
     * @brief Create encrypted backup metadata
     * @param metadata Metadata to encrypt
     * @param password Encryption password
     * @return Encrypted metadata
     */
    QByteArray createEncryptedMetadata(const QByteArray &metadata, const QString &password);

    /**
     * @brief Read encrypted backup metadata
     * @param encryptedMetadata Encrypted metadata
     * @param password Decryption password
     * @return Decrypted metadata
     */
    QByteArray readEncryptedMetadata(const QByteArray &encryptedMetadata, const QString &password);

public slots:
    /**
     * @brief Test encryption/decryption with sample data
     * @param password Test password
     * @return true if test successful
     */
    Q_INVOKABLE bool testEncryption(const QString &password);

signals:
    /**
     * @brief Emitted during file encryption/decryption progress
     * @param progress Progress percentage (0-100)
     */
    void progressChanged(int progress);

    /**
     * @brief Emitted when encryption/decryption status changes
     * @param status Current status message
     */
    void statusChanged(const QString &status);

    /**
     * @brief Emitted when operation completes
     * @param success Whether operation was successful
     * @param message Result message
     */
    void operationCompleted(bool success, const QString &message);

private:
    EncryptionResult encryptAES(const QByteArray &data, const QByteArray &key, const QByteArray &iv);
    EncryptionResult decryptAES(const QByteArray &encryptedData, const QByteArray &key, const QByteArray &iv);
    
    bool processFileEncryption(const QString &inputPath, const QString &outputPath, 
                              const QByteArray &key, const QByteArray &iv, bool encrypt);
    
    QByteArray createFileHeader(const QByteArray &salt, const QByteArray &iv, 
                               EncryptionAlgorithm algorithm);
    bool parseFileHeader(const QByteArray &header, QByteArray &salt, QByteArray &iv, 
                        EncryptionAlgorithm &algorithm);

    EncryptionAlgorithm m_algorithm;
    static const int SALT_LENGTH = 32;
    static const int IV_LENGTH = 16;
    static const int KEY_LENGTH = 32;
    static const int PBKDF2_ITERATIONS = 100000;
    static const QByteArray FILE_MAGIC;
};

Q_DECLARE_METATYPE(EncryptionAlgorithm)
Q_DECLARE_METATYPE(EncryptionResult)

#endif // ENCRYPTION_H
