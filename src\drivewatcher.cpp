#include "drivewatcher.h"
#include <QStorageInfo>
#include <QLoggingCategory>
#include <QDebug>

Q_LOGGING_CATEGORY(driveWatcher, "backuppro.drivewatcher")

DriveWatcher::DriveWatcher(QObject *parent)
    : QObject(parent)
    , m_monitorTimer(new QTimer(this))
    , m_fileSystemWatcher(new QFileSystemWatcher(this))
    , m_isMonitoring(false)
    , m_autoBackupEnabled(false)
{
    // Set up monitoring timer
    m_monitorTimer->setInterval(2000); // Check every 2 seconds
    connect(m_monitorTimer, &QTimer::timeout, this, &DriveWatcher::checkDrives);
    
    // Connect file system watcher
    connect(m_fileSystemWatcher, &QFileSystemWatcher::directoryChanged,
            this, &DriveWatcher::onDirectoryChanged);
    
    qCDebug(driveWatcher) << "DriveWatcher created";
}

DriveWatcher::~DriveWatcher()
{
    stopMonitoring();
    qCDebug(driveWatcher) << "DriveWatcher destroyed";
}

void DriveWatcher::startMonitoring()
{
    if (m_isMonitoring) {
        return;
    }
    
    qCInfo(driveWatcher) << "Starting drive monitoring";
    
    m_isMonitoring = true;
    
    // Initial drive scan
    checkDrives();
    
    // Start periodic monitoring
    m_monitorTimer->start();
    
#ifdef Q_OS_WIN
    setupWindowsMonitoring();
#elif defined(Q_OS_LINUX)
    setupLinuxMonitoring();
#elif defined(Q_OS_MACOS)
    setupMacOSMonitoring();
#endif
}

void DriveWatcher::stopMonitoring()
{
    if (!m_isMonitoring) {
        return;
    }
    
    qCInfo(driveWatcher) << "Stopping drive monitoring";
    
    m_isMonitoring = false;
    m_monitorTimer->stop();
    
#ifdef Q_OS_WIN
    cleanupWindowsMonitoring();
#elif defined(Q_OS_LINUX)
    cleanupLinuxMonitoring();
#elif defined(Q_OS_MACOS)
    cleanupMacOSMonitoring();
#endif
}

QStringList DriveWatcher::availableDrives() const
{
    return m_currentDrives;
}

QVariantList DriveWatcher::getDriveInfo() const
{
    QVariantList result;
    
    for (const auto &info : m_driveInfoCache) {
        result.append(driveInfoToVariantMap(info));
    }
    
    return result;
}

QVariantMap DriveWatcher::getDriveInfo(const QString &drivePath) const
{
    DriveInfo info = getDriveInfoInternal(drivePath);
    return driveInfoToVariantMap(info);
}

bool DriveWatcher::isRemovableDrive(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    return storage.isValid() && !storage.isRoot();
}

bool DriveWatcher::hasEnoughSpace(const QString &drivePath, qint64 requiredSpace) const
{
    QStorageInfo storage(drivePath);
    return storage.isValid() && storage.bytesAvailable() >= requiredSpace;
}

qint64 DriveWatcher::getAvailableSpace(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    return storage.isValid() ? storage.bytesAvailable() : 0;
}

qint64 DriveWatcher::getTotalSpace(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    return storage.isValid() ? storage.bytesTotal() : 0;
}

QString DriveWatcher::getDriveName(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    if (!storage.isValid()) {
        return drivePath;
    }
    
    QString name = storage.displayName();
    if (name.isEmpty()) {
        name = storage.name();
    }
    if (name.isEmpty()) {
        name = drivePath;
    }
    
    return name;
}

bool DriveWatcher::isDriveReady(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    return storage.isValid() && storage.isReady();
}

void DriveWatcher::refreshDrives()
{
    checkDrives();
}

void DriveWatcher::setAutoBackupEnabled(bool enabled, const QString &backupPath)
{
    m_autoBackupEnabled = enabled;
    m_autoBackupSourcePath = backupPath;
    
    qCInfo(driveWatcher) << "Auto backup" << (enabled ? "enabled" : "disabled") 
                        << "with source path:" << backupPath;
}

void DriveWatcher::checkDrives()
{
    QStringList newDrives;
    QList<DriveInfo> newDriveInfo;
    
    // Get all mounted volumes
    QList<QStorageInfo> volumes = QStorageInfo::mountedVolumes();
    
    for (const QStorageInfo &volume : volumes) {
        if (volume.isValid() && volume.isReady()) {
            QString path = volume.rootPath();
            newDrives.append(path);
            
            DriveInfo info = getDriveInfoInternal(path);
            newDriveInfo.append(info);
        }
    }
    
    // Check for changes
    if (newDrives != m_currentDrives) {
        detectDriveChanges();
        m_currentDrives = newDrives;
        m_driveInfoCache = newDriveInfo;
        emit drivesChanged();
    }
}

void DriveWatcher::onDirectoryChanged(const QString &path)
{
    Q_UNUSED(path)
    // Trigger drive check when directory changes are detected
    QTimer::singleShot(500, this, &DriveWatcher::checkDrives);
}

DriveInfo DriveWatcher::getDriveInfoInternal(const QString &drivePath) const
{
    QStorageInfo storage(drivePath);
    
    DriveInfo info;
    info.path = drivePath;
    info.name = storage.name();
    info.fileSystem = QString::fromLocal8Bit(storage.fileSystemType());
    info.totalSpace = storage.bytesTotal();
    info.availableSpace = storage.bytesAvailable();
    info.isRemovable = !storage.isRoot();
    info.isReady = storage.isReady();
    info.displayName = storage.displayName();
    
    if (info.displayName.isEmpty()) {
        info.displayName = info.name;
    }
    if (info.displayName.isEmpty()) {
        info.displayName = drivePath;
    }
    
    return info;
}

QVariantMap DriveWatcher::driveInfoToVariantMap(const DriveInfo &info) const
{
    QVariantMap map;
    map["path"] = info.path;
    map["name"] = info.name;
    map["fileSystem"] = info.fileSystem;
    map["totalSpace"] = info.totalSpace;
    map["availableSpace"] = info.availableSpace;
    map["isRemovable"] = info.isRemovable;
    map["isReady"] = info.isReady;
    map["displayName"] = info.displayName;
    map["totalSpaceFormatted"] = formatBytes(info.totalSpace);
    map["availableSpaceFormatted"] = formatBytes(info.availableSpace);
    
    return map;
}

void DriveWatcher::detectDriveChanges()
{
    // Find newly connected drives
    for (const QString &drive : m_currentDrives) {
        if (!m_currentDrives.contains(drive)) {
            DriveInfo info = getDriveInfoInternal(drive);
            QVariantMap infoMap = driveInfoToVariantMap(info);
            
            emit driveConnected(drive, infoMap);
            
            // Check for auto backup trigger
            if (m_autoBackupEnabled && info.isRemovable && !m_autoBackupSourcePath.isEmpty()) {
                emit autoBackupTriggered(drive);
            }
            
            qCInfo(driveWatcher) << "Drive connected:" << drive;
        }
    }
    
    // Find disconnected drives
    QStringList newDrives;
    QList<QStorageInfo> volumes = QStorageInfo::mountedVolumes();
    for (const QStorageInfo &volume : volumes) {
        if (volume.isValid() && volume.isReady()) {
            newDrives.append(volume.rootPath());
        }
    }
    
    for (const QString &drive : m_currentDrives) {
        if (!newDrives.contains(drive)) {
            emit driveDisconnected(drive);
            qCInfo(driveWatcher) << "Drive disconnected:" << drive;
        }
    }
}

QString DriveWatcher::formatBytes(qint64 bytes) const
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;
    const qint64 TB = GB * 1024;
    
    if (bytes >= TB) {
        return QString::number(bytes / TB, 'f', 1) + " TB";
    } else if (bytes >= GB) {
        return QString::number(bytes / GB, 'f', 1) + " GB";
    } else if (bytes >= MB) {
        return QString::number(bytes / MB, 'f', 1) + " MB";
    } else if (bytes >= KB) {
        return QString::number(bytes / KB, 'f', 1) + " KB";
    } else {
        return QString::number(bytes) + " bytes";
    }
}

// Platform-specific implementations (simplified)
#ifdef Q_OS_WIN
void DriveWatcher::setupWindowsMonitoring()
{
    // TODO: Implement Windows-specific drive monitoring
}

void DriveWatcher::cleanupWindowsMonitoring()
{
    // TODO: Cleanup Windows-specific monitoring
}
#elif defined(Q_OS_LINUX)
void DriveWatcher::setupLinuxMonitoring()
{
    // Monitor /media and /mnt directories
    m_fileSystemWatcher->addPath("/media");
    m_fileSystemWatcher->addPath("/mnt");
}

void DriveWatcher::cleanupLinuxMonitoring()
{
    m_fileSystemWatcher->removePath("/media");
    m_fileSystemWatcher->removePath("/mnt");
}
#elif defined(Q_OS_MACOS)
void DriveWatcher::setupMacOSMonitoring()
{
    // Monitor /Volumes directory
    m_fileSystemWatcher->addPath("/Volumes");
}

void DriveWatcher::cleanupMacOSMonitoring()
{
    m_fileSystemWatcher->removePath("/Volumes");
}
#endif
