QT += core gui quick qml widgets network concurrent

CONFIG += c++17

TARGET = BackupPro
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Refer to the documentation for the
# deprecated API to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Include paths
INCLUDEPATH += include

# Source files
SOURCES += \
    src/main.cpp \
    src/appcore.cpp \
    src/backupmanager.cpp \
    src/drivewatcher.cpp \
    src/fileoperations.cpp \
    src/scheduler.cpp \
    src/settings.cpp \
    src/encryption.cpp

# Header files
HEADERS += \
    include/appcore.h \
    include/backupmanager.h \
    include/drivewatcher.h \
    include/fileoperations.h \
    include/scheduler.h \
    include/settings.h \
    include/encryption.h

# QML files
QML_FILES += \
    resources/qml/main.qml \
    resources/qml/BackupPage.qml \
    resources/qml/RestorePage.qml \
    resources/qml/SchedulePage.qml \
    resources/qml/HistoryPage.qml \
    resources/qml/SettingsPage.qml \
    resources/qml/components/NavButton.qml \
    resources/qml/components/DriveSelector.qml \
    resources/qml/components/ProgressIndicator.qml \
    resources/qml/components/BackupItemDelegate.qml

# Resources
RESOURCES += resources/qml.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# Platform specific configurations
win32 {
    RC_FILE = deployment/windows/resource.rc
    QMAKE_TARGET_PRODUCT = BackupPro
    QMAKE_TARGET_DESCRIPTION = Professional Backup Solution
    QMAKE_TARGET_COPYRIGHT = Copyright (c) 2024
}

macx {
    QMAKE_INFO_PLIST = deployment/macos/Info.plist
    ICON = resources/icons/app-icon.icns
}

unix:!macx {
    desktop.files = deployment/linux/backuppro.desktop
    desktop.path = /usr/share/applications
    INSTALLS += desktop
}
