# Qt-specific
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
.qmake.cache
.qmake.stash

# Qt Creator
CMakeLists.txt.user*
*.autosave

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Build directories
build/
build-*/
debug/
release/
Debug/
Release/

# Visual Studio
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
.vs/

# Xcode
*.xcodeproj/
*.xcworkspace/
DerivedData/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# GCC/Clang
*.o
*.obj
*.so
*.dylib
*.dll
*.exe
*.app
*.a
*.lib

# Temporary files
*~
*.tmp
*.temp
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.sublime-*

# Logs
*.log

# Package files
*.tar.gz
*.zip
*.7z
*.rar

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
