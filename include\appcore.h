#ifndef APPCORE_H
#define APPCORE_H

#include <QObject>
#include <QQmlApplicationEngine>
#include <QGuiApplication>
#include <QQmlContext>
#include <QTimer>
#include <memory>

class BackupManager;
class DriveWatcher;
class Scheduler;
class Settings;

/**
 * @brief The AppCore class serves as the main application coordinator
 * 
 * This class manages the overall application lifecycle, coordinates between
 * different components, and provides the bridge between C++ backend and QML frontend.
 */
class AppCore : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isBackupInProgress READ isBackupInProgress NOTIFY backupProgressChanged)
    Q_PROPERTY(QString currentStatus READ currentStatus NOTIFY statusChanged)
    Q_PROPERTY(int backupProgress READ backupProgress NOTIFY backupProgressChanged)

public:
    explicit AppCore(QObject *parent = nullptr);
    ~AppCore();

    /**
     * @brief Initialize the application core
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Start the QML application engine
     * @return true if started successfully, false otherwise
     */
    bool startEngine();

    // Property getters
    bool isBackupInProgress() const { return m_isBackupInProgress; }
    QString currentStatus() const { return m_currentStatus; }
    int backupProgress() const { return m_backupProgress; }

public slots:
    /**
     * @brief Start a backup operation
     * @param sourcePath Source directory path
     * @param destinationPath Destination directory path
     * @param encrypted Whether to encrypt the backup
     */
    Q_INVOKABLE void startBackup(const QString &sourcePath, const QString &destinationPath, bool encrypted = false);

    /**
     * @brief Stop the current backup operation
     */
    Q_INVOKABLE void stopBackup();

    /**
     * @brief Start a restore operation
     * @param backupPath Path to the backup
     * @param restorePath Path where to restore
     */
    Q_INVOKABLE void startRestore(const QString &backupPath, const QString &restorePath);

    /**
     * @brief Get list of available drives
     * @return QStringList of drive paths
     */
    Q_INVOKABLE QStringList getAvailableDrives();

    /**
     * @brief Get backup history
     * @return QVariantList of backup records
     */
    Q_INVOKABLE QVariantList getBackupHistory();

    /**
     * @brief Save application settings
     * @param key Setting key
     * @param value Setting value
     */
    Q_INVOKABLE void saveSetting(const QString &key, const QVariant &value);

    /**
     * @brief Load application setting
     * @param key Setting key
     * @param defaultValue Default value if key doesn't exist
     * @return Setting value
     */
    Q_INVOKABLE QVariant loadSetting(const QString &key, const QVariant &defaultValue = QVariant());

signals:
    void backupProgressChanged();
    void statusChanged();
    void backupCompleted(bool success, const QString &message);
    void restoreCompleted(bool success, const QString &message);
    void driveConnected(const QString &drivePath);
    void driveDisconnected(const QString &drivePath);

private slots:
    void onBackupProgress(int progress);
    void onBackupCompleted(bool success, const QString &message);
    void onRestoreCompleted(bool success, const QString &message);
    void onStatusChanged(const QString &status);

private:
    void setupQmlContext();
    void connectSignals();

    QQmlApplicationEngine *m_engine;
    std::unique_ptr<BackupManager> m_backupManager;
    std::unique_ptr<DriveWatcher> m_driveWatcher;
    std::unique_ptr<Scheduler> m_scheduler;
    std::unique_ptr<Settings> m_settings;

    bool m_isBackupInProgress;
    QString m_currentStatus;
    int m_backupProgress;
};

#endif // APPCORE_H
