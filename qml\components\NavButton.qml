import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Button {
    id: navButton
    
    property string icon: ""
    property bool active: false
    
    width: parent.width
    height: 48
    
    flat: true
    
    // Colors based on main window theme
    readonly property color primaryColor: "#2196F3"
    readonly property color backgroundColor: parent.parent.parent.isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: parent.parent.parent.isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: parent.parent.parent.isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: parent.parent.parent.isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: parent.parent.parent.isDarkTheme ? "#404040" : "#e0e0e0"
    
    background: Rectangle {
        color: {
            if (navButton.active) {
                return Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1)
            } else if (navButton.hovered) {
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.05)
            } else {
                return "transparent"
            }
        }
        
        border.color: navButton.active ? primaryColor : "transparent"
        border.width: navButton.active ? 2 : 0
        radius: 6
        
        Behavior on color {
            ColorAnimation { duration: 150 }
        }
        
        Behavior on border.color {
            ColorAnimation { duration: 150 }
        }
    }
    
    contentItem: RowLayout {
        spacing: 12
        
        Image {
            source: navButton.icon
            Layout.preferredWidth: 20
            Layout.preferredHeight: 20
            fillMode: Image.PreserveAspectFit
            
            ColorOverlay {
                anchors.fill: parent
                source: parent
                color: navButton.active ? primaryColor : textColor
                
                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
        }
        
        Label {
            text: navButton.text
            color: navButton.active ? primaryColor : textColor
            font.pixelSize: 14
            font.weight: navButton.active ? Font.Medium : Font.Normal
            Layout.fillWidth: true
            
            Behavior on color {
                ColorAnimation { duration: 150 }
            }
        }
    }
    
    // Hover effects
    HoverHandler {
        id: hoverHandler
        cursorShape: Qt.PointingHandCursor
    }
    
    // Click animation
    PropertyAnimation {
        id: clickAnimation
        target: navButton
        property: "scale"
        from: 1.0
        to: 0.95
        duration: 100
        easing.type: Easing.OutQuad
        
        onFinished: {
            PropertyAnimation {
                target: navButton
                property: "scale"
                from: 0.95
                to: 1.0
                duration: 100
                easing.type: Easing.OutQuad
            }.start()
        }
    }
    
    onPressed: clickAnimation.start()
    
    // Accessibility
    Accessible.role: Accessible.Button
    Accessible.name: text
    Accessible.description: qsTr("Navigate to %1 page").arg(text)
}
