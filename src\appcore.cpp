#include "appcore.h"
#include "backupmanager.h"
#include "drivewatcher.h"
#include "scheduler.h"
#include "settings.h"

#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QUrl>
#include <QDebug>
#include <QLoggingCategory>

Q_LOGGING_CATEGORY(appCore, "backuppro.appcore")

AppCore::AppCore(QObject *parent)
    : QObject(parent)
    , m_engine(nullptr)
    , m_isBackupInProgress(false)
    , m_currentStatus("Ready")
    , m_backupProgress(0)
{
    qCDebug(appCore) << "AppCore created";
}

AppCore::~AppCore()
{
    qCDebug(appCore) << "AppCore destroyed";
}

bool AppCore::initialize()
{
    qCDebug(appCore) << "Initializing AppCore...";

    try {
        // Create core components
        m_settings = std::make_unique<Settings>(this);
        m_settings->initialize();

        m_backupManager = std::make_unique<BackupManager>(this);
        m_driveWatcher = std::make_unique<DriveWatcher>(this);
        m_scheduler = std::make_unique<Scheduler>(this);

        // Initialize components
        if (!m_scheduler->initialize()) {
            qCCritical(appCore) << "Failed to initialize scheduler";
            return false;
        }

        // Connect signals
        connectSignals();

        // Start drive monitoring
        m_driveWatcher->startMonitoring();

        // Start scheduler
        m_scheduler->start();

        qCInfo(appCore) << "AppCore initialized successfully";
        return true;
    }
    catch (const std::exception &e) {
        qCCritical(appCore) << "Exception during initialization:" << e.what();
        return false;
    }
}

bool AppCore::startEngine()
{
    qCDebug(appCore) << "Starting QML engine...";

    try {
        m_engine = new QQmlApplicationEngine(this);

        // Setup QML context
        setupQmlContext();

        // Load main QML file
        const QUrl url(QStringLiteral("qrc:/resources/qml/main.qml"));
        
        QObject::connect(m_engine, &QQmlApplicationEngine::objectCreated,
                        this, [url](QObject *obj, const QUrl &objUrl) {
            if (!obj && url == objUrl) {
                qCCritical(appCore) << "Failed to load main QML file";
                QCoreApplication::exit(-1);
            }
        });

        m_engine->load(url);

        if (m_engine->rootObjects().isEmpty()) {
            qCCritical(appCore) << "No root objects found in QML engine";
            return false;
        }

        qCInfo(appCore) << "QML engine started successfully";
        return true;
    }
    catch (const std::exception &e) {
        qCCritical(appCore) << "Exception during engine startup:" << e.what();
        return false;
    }
}

void AppCore::setupQmlContext()
{
    if (!m_engine) {
        qCWarning(appCore) << "Cannot setup QML context: engine is null";
        return;
    }

    QQmlContext *context = m_engine->rootContext();
    
    // Register this object as the main app core
    context->setContextProperty("appCore", this);
    
    // Register other components
    context->setContextProperty("backupManager", m_backupManager.get());
    context->setContextProperty("driveWatcher", m_driveWatcher.get());
    context->setContextProperty("scheduler", m_scheduler.get());
    context->setContextProperty("settings", m_settings.get());

    qCDebug(appCore) << "QML context setup complete";
}

void AppCore::connectSignals()
{
    // Connect backup manager signals
    connect(m_backupManager.get(), &BackupManager::progressChanged,
            this, &AppCore::onBackupProgress);
    connect(m_backupManager.get(), &BackupManager::backupCompleted,
            this, &AppCore::onBackupCompleted);
    connect(m_backupManager.get(), &BackupManager::statusChanged,
            this, &AppCore::onStatusChanged);

    // Connect drive watcher signals
    connect(m_driveWatcher.get(), &DriveWatcher::driveConnected,
            this, &AppCore::driveConnected);
    connect(m_driveWatcher.get(), &DriveWatcher::driveDisconnected,
            this, &AppCore::driveDisconnected);

    // Connect scheduler signals
    connect(m_scheduler.get(), &Scheduler::scheduleTriggered,
            this, [this](const BackupSchedule &schedule) {
                qCInfo(appCore) << "Schedule triggered:" << schedule.name;
                startBackup(schedule.sourcePath, schedule.destinationPath, schedule.encrypted);
            });

    qCDebug(appCore) << "Signal connections established";
}

void AppCore::startBackup(const QString &sourcePath, const QString &destinationPath, bool encrypted)
{
    qCInfo(appCore) << "Starting backup:" << sourcePath << "->" << destinationPath << "encrypted:" << encrypted;
    
    if (m_isBackupInProgress) {
        qCWarning(appCore) << "Backup already in progress";
        return;
    }

    m_backupManager->startBackup(sourcePath, destinationPath, encrypted);
}

void AppCore::stopBackup()
{
    qCInfo(appCore) << "Stopping backup";
    m_backupManager->stopBackup();
}

void AppCore::startRestore(const QString &backupPath, const QString &restorePath)
{
    qCInfo(appCore) << "Starting restore:" << backupPath << "->" << restorePath;
    // TODO: Implement restore functionality
    emit restoreCompleted(false, "Restore functionality not yet implemented");
}

QStringList AppCore::getAvailableDrives()
{
    return m_driveWatcher->availableDrives();
}

QVariantList AppCore::getBackupHistory()
{
    return m_backupManager->getBackupHistory();
}

void AppCore::saveSetting(const QString &key, const QVariant &value)
{
    m_settings->setValue(key, value);
}

QVariant AppCore::loadSetting(const QString &key, const QVariant &defaultValue)
{
    return m_settings->getValue(key, defaultValue);
}

void AppCore::onBackupProgress(int progress)
{
    if (m_backupProgress != progress) {
        m_backupProgress = progress;
        emit backupProgressChanged();
    }
}

void AppCore::onBackupCompleted(bool success, const QString &message)
{
    m_isBackupInProgress = false;
    m_backupProgress = success ? 100 : 0;
    m_currentStatus = success ? "Backup completed" : "Backup failed";
    
    emit backupProgressChanged();
    emit statusChanged();
    emit backupCompleted(success, message);
    
    qCInfo(appCore) << "Backup completed:" << success << message;
}

void AppCore::onRestoreCompleted(bool success, const QString &message)
{
    emit restoreCompleted(success, message);
    qCInfo(appCore) << "Restore completed:" << success << message;
}

void AppCore::onStatusChanged(const QString &status)
{
    if (m_currentStatus != status) {
        m_currentStatus = status;
        emit statusChanged();
    }
}
