import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import BackupPro 1.0

Page {
    id: historyPage
    
    property bool isDarkTheme: parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color successColor: "#4CAF50"
    readonly property color errorColor: "#F44336"
    
    background: Rectangle {
        color: backgroundColor
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 24
        
        ColumnLayout {
            width: parent.width
            spacing: 24
            
            // Page header
            RowLayout {
                Layout.fillWidth: true
                
                Label {
                    text: qsTr("Backup History")
                    font.pixelSize: 28
                    font.bold: true
                    color: textColor
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Refresh")
                    icon.source: "qrc:/icons/refresh.png"
                    
                    background: Rectangle {
                        color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                        border.color: primaryColor
                        border.width: 1
                        radius: 6
                    }
                    
                    contentItem: RowLayout {
                        spacing: 8
                        
                        Image {
                            source: parent.parent.icon.source
                            Layout.preferredWidth: 16
                            Layout.preferredHeight: 16
                            fillMode: Image.PreserveAspectFit
                            
                            ColorOverlay {
                                anchors.fill: parent
                                source: parent
                                color: primaryColor
                            }
                        }
                        
                        Label {
                            text: parent.parent.text
                            color: primaryColor
                            font.pixelSize: 14
                        }
                    }
                    
                    onClicked: {
                        // TODO: Refresh backup history
                        console.log("Refreshing backup history")
                    }
                }
            }
            
            // Backup history list
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Recent Backups")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ListView {
                    anchors.fill: parent
                    anchors.margins: 16
                    model: ListModel {
                        id: historyModel
                        
                        ListElement {
                            timestamp: "2024-01-15 14:30:00"
                            sourcePath: "C:/Users/<USER>/Documents"
                            destinationPath: "D:/Backups/Documents_20240115_143000"
                            status: "Success"
                            fileCount: 1247
                            totalSize: "2.3 GB"
                            duration: "5 minutes"
                            encrypted: true
                        }
                        
                        ListElement {
                            timestamp: "2024-01-14 02:00:00"
                            sourcePath: "C:/Users/<USER>/Pictures"
                            destinationPath: "E:/Backups/Pictures_20240114_020000"
                            status: "Success"
                            fileCount: 3456
                            totalSize: "15.7 GB"
                            duration: "23 minutes"
                            encrypted: false
                        }
                        
                        ListElement {
                            timestamp: "2024-01-13 18:45:00"
                            sourcePath: "C:/Projects"
                            destinationPath: "D:/Backups/Projects_20240113_184500"
                            status: "Failed"
                            fileCount: 0
                            totalSize: "0 MB"
                            duration: "1 minute"
                            encrypted: false
                        }
                    }
                    
                    delegate: Rectangle {
                        width: parent.width
                        height: 100
                        color: "transparent"
                        border.color: borderColor
                        border.width: 1
                        radius: 6
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 16
                            
                            // Status indicator
                            Rectangle {
                                width: 16
                                height: 16
                                radius: 8
                                color: model.status === "Success" ? successColor : errorColor
                                Layout.alignment: Qt.AlignTop
                                
                                Image {
                                    anchors.centerIn: parent
                                    width: 10
                                    height: 10
                                    source: model.status === "Success" ? "qrc:/icons/check.png" : "qrc:/icons/error.png"
                                    fillMode: Image.PreserveAspectFit
                                    
                                    ColorOverlay {
                                        anchors.fill: parent
                                        source: parent
                                        color: "white"
                                    }
                                }
                            }
                            
                            // Backup info
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4
                                
                                RowLayout {
                                    Layout.fillWidth: true
                                    
                                    Label {
                                        text: Qt.formatDateTime(new Date(model.timestamp), "MMM dd, yyyy hh:mm AP")
                                        color: textColor
                                        font.pixelSize: 14
                                        font.bold: true
                                    }
                                    
                                    Item { Layout.fillWidth: true }
                                    
                                    Label {
                                        text: model.status
                                        color: model.status === "Success" ? successColor : errorColor
                                        font.pixelSize: 12
                                        font.bold: true
                                    }
                                }
                                
                                Label {
                                    text: qsTr("From: %1").arg(model.sourcePath)
                                    color: secondaryTextColor
                                    font.pixelSize: 11
                                    elide: Text.ElideMiddle
                                    Layout.fillWidth: true
                                }
                                
                                Label {
                                    text: qsTr("To: %1").arg(model.destinationPath)
                                    color: secondaryTextColor
                                    font.pixelSize: 11
                                    elide: Text.ElideMiddle
                                    Layout.fillWidth: true
                                }
                                
                                RowLayout {
                                    spacing: 16
                                    
                                    Label {
                                        text: qsTr("%1 files").arg(model.fileCount.toLocaleString())
                                        color: secondaryTextColor
                                        font.pixelSize: 11
                                    }
                                    
                                    Label {
                                        text: model.totalSize
                                        color: secondaryTextColor
                                        font.pixelSize: 11
                                    }
                                    
                                    Label {
                                        text: qsTr("Duration: %1").arg(model.duration)
                                        color: secondaryTextColor
                                        font.pixelSize: 11
                                    }
                                    
                                    Label {
                                        text: model.encrypted ? qsTr("Encrypted") : qsTr("Not encrypted")
                                        color: model.encrypted ? successColor : secondaryTextColor
                                        font.pixelSize: 11
                                        font.bold: model.encrypted
                                    }
                                }
                            }
                            
                            // Action buttons
                            ColumnLayout {
                                spacing: 4
                                
                                Button {
                                    text: qsTr("Restore")
                                    enabled: model.status === "Success"
                                    
                                    background: Rectangle {
                                        color: parent.enabled ? (parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent") : "transparent"
                                        border.color: parent.enabled ? primaryColor : borderColor
                                        border.width: 1
                                        radius: 4
                                    }
                                    
                                    contentItem: Label {
                                        text: parent.text
                                        color: parent.enabled ? primaryColor : secondaryTextColor
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    
                                    onClicked: {
                                        // TODO: Navigate to restore page with this backup
                                        console.log("Restoring backup from:", model.destinationPath)
                                    }
                                }
                                
                                Button {
                                    text: qsTr("Delete")
                                    enabled: model.status === "Success"
                                    
                                    background: Rectangle {
                                        color: parent.enabled ? (parent.hovered ? Qt.rgba(errorColor.r, errorColor.g, errorColor.b, 0.1) : "transparent") : "transparent"
                                        border.color: parent.enabled ? errorColor : borderColor
                                        border.width: 1
                                        radius: 4
                                    }
                                    
                                    contentItem: Label {
                                        text: parent.text
                                        color: parent.enabled ? errorColor : secondaryTextColor
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    
                                    onClicked: {
                                        // TODO: Delete backup
                                        console.log("Deleting backup:", model.destinationPath)
                                    }
                                }
                            }
                        }
                    }
                    
                    // Empty state
                    Label {
                        anchors.centerIn: parent
                        text: qsTr("No backup history available")
                        color: secondaryTextColor
                        font.pixelSize: 14
                        visible: parent.count === 0
                    }
                }
            }
            
            Item { Layout.fillHeight: true }
        }
    }
}
