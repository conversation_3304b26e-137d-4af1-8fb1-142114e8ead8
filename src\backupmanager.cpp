#include "backupmanager.h"
#include "fileoperations.h"
#include "encryption.h"

#include <QThread>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDir>
#include <QUuid>
#include <QLoggingCategory>

Q_LOGGING_CATEGORY(backupManager, "backuppro.backupmanager")

BackupManager::BackupManager(QObject *parent)
    : QObject(parent)
    , m_backupThread(nullptr)
    , m_currentEncrypted(false)
    , m_currentIncremental(true)
    , m_isBackupInProgress(false)
    , m_stopRequested(false)
    , m_currentProgress(0)
    , m_totalBytesToProcess(0)
    , m_bytesProcessed(0)
    , m_scheduleTimer(new QTimer(this))
{
    m_fileOperations = std::make_unique<FileOperations>(this);
    m_encryption = std::make_unique<Encryption>(this);
    
    // Connect file operations signals
    connect(m_fileOperations.get(), &FileOperations::progressChanged,
            this, &BackupManager::onFileProgress);
    
    loadBackupHistory();
    
    qCDebug(backupManager) << "BackupManager created";
}

BackupManager::~BackupManager()
{
    if (m_backupThread && m_backupThread->isRunning()) {
        m_stopRequested = true;
        m_backupThread->quit();
        m_backupThread->wait(5000);
    }
    qCDebug(backupManager) << "BackupManager destroyed";
}

void BackupManager::startBackup(const QString &sourcePath, const QString &destinationPath, 
                                bool encrypted, bool incremental)
{
    if (m_isBackupInProgress) {
        qCWarning(backupManager) << "Backup already in progress";
        return;
    }

    qCInfo(backupManager) << "Starting backup:" << sourcePath << "->" << destinationPath;

    m_currentSourcePath = sourcePath;
    m_currentDestinationPath = destinationPath;
    m_currentEncrypted = encrypted;
    m_currentIncremental = incremental;
    m_isBackupInProgress = true;
    m_stopRequested = false;
    m_currentProgress = 0;
    m_bytesProcessed = 0;

    // Calculate total size
    m_totalBytesToProcess = m_fileOperations->getDirectorySize(sourcePath);
    
    emit statusChanged("Preparing backup...");
    emit backupStarted(sourcePath, destinationPath);

    // Start backup in separate thread
    m_backupThread = QThread::create([this]() {
        performBackup();
    });
    
    connect(m_backupThread, &QThread::finished, 
            this, &BackupManager::onBackupThreadFinished);
    
    m_backupThread->start();
}

void BackupManager::stopBackup()
{
    if (!m_isBackupInProgress) {
        return;
    }

    qCInfo(backupManager) << "Stopping backup";
    m_stopRequested = true;
    m_fileOperations->cancelOperation();
    
    if (m_backupThread) {
        m_backupThread->quit();
        m_backupThread->wait(5000);
    }
}

bool BackupManager::isBackupInProgress() const
{
    return m_isBackupInProgress;
}

QVariantList BackupManager::getBackupHistory() const
{
    QMutexLocker locker(&m_historyMutex);
    QVariantList result;
    
    for (const auto &record : m_backupHistory) {
        QVariantMap item;
        item["id"] = record.id;
        item["sourcePath"] = record.sourcePath;
        item["destinationPath"] = record.destinationPath;
        item["timestamp"] = record.timestamp;
        item["totalSize"] = record.totalSize;
        item["fileCount"] = record.fileCount;
        item["encrypted"] = record.encrypted;
        item["successful"] = record.successful;
        item["errorMessage"] = record.errorMessage;
        result.append(item);
    }
    
    return result;
}

BackupRecord BackupManager::getBackupRecord(const QString &backupId) const
{
    QMutexLocker locker(&m_historyMutex);
    
    for (const auto &record : m_backupHistory) {
        if (record.id == backupId) {
            return record;
        }
    }
    
    return BackupRecord(); // Return empty record if not found
}

bool BackupManager::verifyBackup(const QString &backupPath)
{
    // TODO: Implement backup verification
    Q_UNUSED(backupPath)
    return true;
}

bool BackupManager::deleteBackup(const QString &backupId)
{
    // TODO: Implement backup deletion
    Q_UNUSED(backupId)
    return false;
}

qint64 BackupManager::getEstimatedBackupSize(const QString &sourcePath)
{
    return m_fileOperations->getDirectorySize(sourcePath);
}

void BackupManager::scheduleBackup(const QString &sourcePath, const QString &destinationPath, int intervalHours)
{
    // TODO: Implement backup scheduling
    Q_UNUSED(sourcePath)
    Q_UNUSED(destinationPath)
    Q_UNUSED(intervalHours)
}

void BackupManager::performBackup()
{
    qCDebug(backupManager) << "Performing backup in thread";
    
    bool success = false;
    QString errorMessage;
    
    try {
        emit statusChanged("Starting backup...");
        
        // Create destination directory if it doesn't exist
        QDir destDir(m_currentDestinationPath);
        if (!destDir.exists()) {
            if (!destDir.mkpath(".")) {
                throw std::runtime_error("Failed to create destination directory");
            }
        }
        
        // Perform the backup
        if (m_currentIncremental) {
            success = createIncrementalBackup(m_currentSourcePath, m_currentDestinationPath);
        } else {
            success = createFullBackup(m_currentSourcePath, m_currentDestinationPath);
        }
        
        if (success && !m_stopRequested) {
            emit statusChanged("Backup completed successfully");
            errorMessage = "Backup completed successfully";
        } else if (m_stopRequested) {
            success = false;
            errorMessage = "Backup was cancelled";
        }
        
    } catch (const std::exception &e) {
        success = false;
        errorMessage = QString("Backup failed: %1").arg(e.what());
        qCCritical(backupManager) << "Backup exception:" << e.what();
    }
    
    // Create backup record
    BackupRecord record;
    record.id = generateBackupId();
    record.sourcePath = m_currentSourcePath;
    record.destinationPath = m_currentDestinationPath;
    record.timestamp = QDateTime::currentDateTime();
    record.totalSize = m_bytesProcessed;
    record.fileCount = m_fileOperations->getFileCount(m_currentSourcePath);
    record.encrypted = m_currentEncrypted;
    record.successful = success;
    record.errorMessage = errorMessage;
    
    saveBackupRecord(record);
    
    emit backupCompleted(success, errorMessage);
}

bool BackupManager::createIncrementalBackup(const QString &sourcePath, const QString &destinationPath)
{
    emit statusChanged("Creating incremental backup...");
    
    CopyResult result = m_fileOperations->copyFilesRecursive(sourcePath, destinationPath, false);
    return result.success;
}

bool BackupManager::createFullBackup(const QString &sourcePath, const QString &destinationPath)
{
    emit statusChanged("Creating full backup...");
    
    CopyResult result = m_fileOperations->copyFilesRecursive(sourcePath, destinationPath, true);
    return result.success;
}

void BackupManager::onFileProgress(const QString &fileName, qint64 bytesProcessed, qint64 totalBytes)
{
    Q_UNUSED(fileName)
    Q_UNUSED(totalBytes)
    
    m_bytesProcessed += bytesProcessed;
    
    if (m_totalBytesToProcess > 0) {
        int progress = static_cast<int>((m_bytesProcessed * 100) / m_totalBytesToProcess);
        if (progress != m_currentProgress) {
            m_currentProgress = progress;
            emit progressChanged(progress);
        }
    }
}

void BackupManager::onBackupThreadFinished()
{
    m_isBackupInProgress = false;
    
    if (m_backupThread) {
        m_backupThread->deleteLater();
        m_backupThread = nullptr;
    }
    
    qCDebug(backupManager) << "Backup thread finished";
}

void BackupManager::saveBackupRecord(const BackupRecord &record)
{
    QMutexLocker locker(&m_historyMutex);
    
    m_backupHistory.prepend(record);
    
    // Keep only the last 100 records
    if (m_backupHistory.size() > 100) {
        m_backupHistory.removeLast();
    }
    
    // Save to file
    QString historyPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/backup_history.json";
    
    QJsonArray historyArray;
    for (const auto &rec : m_backupHistory) {
        QJsonObject obj;
        obj["id"] = rec.id;
        obj["sourcePath"] = rec.sourcePath;
        obj["destinationPath"] = rec.destinationPath;
        obj["timestamp"] = rec.timestamp.toString(Qt::ISODate);
        obj["totalSize"] = rec.totalSize;
        obj["fileCount"] = rec.fileCount;
        obj["encrypted"] = rec.encrypted;
        obj["successful"] = rec.successful;
        obj["errorMessage"] = rec.errorMessage;
        historyArray.append(obj);
    }
    
    QJsonDocument doc(historyArray);
    
    QFile file(historyPath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
    }
}

void BackupManager::loadBackupHistory()
{
    QString historyPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/backup_history.json";
    
    QFile file(historyPath);
    if (!file.open(QIODevice::ReadOnly)) {
        return;
    }
    
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    if (!doc.isArray()) {
        return;
    }
    
    QMutexLocker locker(&m_historyMutex);
    m_backupHistory.clear();
    
    QJsonArray historyArray = doc.array();
    for (const auto &value : historyArray) {
        QJsonObject obj = value.toObject();
        
        BackupRecord record;
        record.id = obj["id"].toString();
        record.sourcePath = obj["sourcePath"].toString();
        record.destinationPath = obj["destinationPath"].toString();
        record.timestamp = QDateTime::fromString(obj["timestamp"].toString(), Qt::ISODate);
        record.totalSize = obj["totalSize"].toInteger();
        record.fileCount = obj["fileCount"].toInt();
        record.encrypted = obj["encrypted"].toBool();
        record.successful = obj["successful"].toBool();
        record.errorMessage = obj["errorMessage"].toString();
        
        m_backupHistory.append(record);
    }
    
    qCDebug(backupManager) << "Loaded" << m_backupHistory.size() << "backup records";
}

QString BackupManager::generateBackupId() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}
