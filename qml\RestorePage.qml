import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs
import BackupPro 1.0

Page {
    id: restorePage
    
    property bool isDarkTheme: parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color accentColor: "#FF9800"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color successColor: "#4CAF50"
    readonly property color warningColor: "#FF9800"
    readonly property color errorColor: "#F44336"
    
    background: Rectangle {
        color: backgroundColor
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 24
        
        ColumnLayout {
            width: parent.width
            spacing: 24
            
            // Page header
            RowLayout {
                Layout.fillWidth: true
                
                Label {
                    text: qsTr("Restore")
                    font.pixelSize: 28
                    font.bold: true
                    color: textColor
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Browse Backups")
                    icon.source: "qrc:/icons/folder.png"
                    
                    background: Rectangle {
                        color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                        border.color: primaryColor
                        border.width: 1
                        radius: 6
                    }
                    
                    contentItem: RowLayout {
                        spacing: 8
                        
                        Image {
                            source: parent.parent.icon.source
                            Layout.preferredWidth: 16
                            Layout.preferredHeight: 16
                            fillMode: Image.PreserveAspectFit
                            
                            ColorOverlay {
                                anchors.fill: parent
                                source: parent
                                color: primaryColor
                            }
                        }
                        
                        Label {
                            text: parent.parent.text
                            color: primaryColor
                            font.pixelSize: 14
                        }
                    }
                    
                    onClicked: backupFolderDialog.open()
                }
            }
            
            // Restore configuration
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Restore Configuration")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 16
                    
                    // Backup source selection
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8
                        
                        Label {
                            text: qsTr("Backup Source")
                            color: textColor
                            font.pixelSize: 14
                            font.bold: true
                        }
                        
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 8
                            
                            TextField {
                                id: backupSourceField
                                Layout.fillWidth: true
                                placeholderText: qsTr("Select backup to restore...")
                                color: textColor
                                
                                background: Rectangle {
                                    color: backgroundColor
                                    border.color: parent.activeFocus ? primaryColor : borderColor
                                    border.width: parent.activeFocus ? 2 : 1
                                    radius: 4
                                    
                                    Behavior on border.color {
                                        ColorAnimation { duration: 150 }
                                    }
                                }
                            }
                            
                            Button {
                                text: qsTr("Browse")
                                onClicked: backupFolderDialog.open()
                                
                                background: Rectangle {
                                    color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                    border.color: primaryColor
                                    border.width: 1
                                    radius: 4
                                }
                                
                                contentItem: Label {
                                    text: parent.text
                                    color: primaryColor
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                        }
                    }
                    
                    // Restore destination
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8
                        
                        Label {
                            text: qsTr("Restore Destination")
                            color: textColor
                            font.pixelSize: 14
                            font.bold: true
                        }
                        
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 8
                            
                            TextField {
                                id: restoreDestinationField
                                Layout.fillWidth: true
                                placeholderText: qsTr("Select restore destination...")
                                color: textColor
                                
                                background: Rectangle {
                                    color: backgroundColor
                                    border.color: parent.activeFocus ? primaryColor : borderColor
                                    border.width: parent.activeFocus ? 2 : 1
                                    radius: 4
                                    
                                    Behavior on border.color {
                                        ColorAnimation { duration: 150 }
                                    }
                                }
                            }
                            
                            Button {
                                text: qsTr("Browse")
                                onClicked: restoreFolderDialog.open()
                                
                                background: Rectangle {
                                    color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                    border.color: primaryColor
                                    border.width: 1
                                    radius: 4
                                }
                                
                                contentItem: Label {
                                    text: parent.text
                                    color: primaryColor
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                        }
                    }
                    
                    // Restore options
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 24
                        
                        CheckBox {
                            id: overwriteCheckBox
                            text: qsTr("Overwrite Existing Files")
                            
                            indicator: Rectangle {
                                implicitWidth: 20
                                implicitHeight: 20
                                x: parent.leftPadding
                                y: parent.height / 2 - height / 2
                                radius: 3
                                border.color: parent.checked ? primaryColor : borderColor
                                border.width: parent.checked ? 2 : 1
                                color: parent.checked ? primaryColor : "transparent"
                                
                                Image {
                                    width: 12
                                    height: 12
                                    anchors.centerIn: parent
                                    source: "qrc:/icons/check.png"
                                    visible: parent.parent.checked
                                    
                                    ColorOverlay {
                                        anchors.fill: parent
                                        source: parent
                                        color: "white"
                                    }
                                }
                            }
                            
                            contentItem: Label {
                                text: parent.text
                                color: textColor
                                leftPadding: parent.indicator.width + parent.spacing
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                        
                        CheckBox {
                            id: preservePermissionsCheckBox
                            text: qsTr("Preserve Permissions")
                            checked: true
                            
                            indicator: Rectangle {
                                implicitWidth: 20
                                implicitHeight: 20
                                x: parent.leftPadding
                                y: parent.height / 2 - height / 2
                                radius: 3
                                border.color: parent.checked ? primaryColor : borderColor
                                border.width: parent.checked ? 2 : 1
                                color: parent.checked ? primaryColor : "transparent"
                                
                                Image {
                                    width: 12
                                    height: 12
                                    anchors.centerIn: parent
                                    source: "qrc:/icons/check.png"
                                    visible: parent.parent.checked
                                    
                                    ColorOverlay {
                                        anchors.fill: parent
                                        source: parent
                                        color: "white"
                                    }
                                }
                            }
                            
                            contentItem: Label {
                                text: parent.text
                                color: textColor
                                leftPadding: parent.indicator.width + parent.spacing
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                        
                        Item { Layout.fillWidth: true }
                    }
                }
            }
            
            // Action buttons
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                Button {
                    text: qsTr("Start Restore")
                    enabled: backupSourceField.text.length > 0 && restoreDestinationField.text.length > 0
                    Layout.preferredWidth: 150
                    
                    background: Rectangle {
                        color: parent.enabled ? (parent.hovered ? Qt.darker(successColor, 1.1) : successColor) : borderColor
                        radius: 6
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        // TODO: Implement restore functionality
                        console.log("Starting restore from", backupSourceField.text, "to", restoreDestinationField.text)
                    }
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Verify Backup")
                    enabled: backupSourceField.text.length > 0
                    
                    background: Rectangle {
                        color: parent.enabled ? (parent.hovered ? Qt.rgba(accentColor.r, accentColor.g, accentColor.b, 0.1) : "transparent") : "transparent"
                        border.color: parent.enabled ? accentColor : borderColor
                        border.width: 1
                        radius: 6
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: parent.enabled ? accentColor : secondaryTextColor
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        // TODO: Implement backup verification
                        console.log("Verifying backup at", backupSourceField.text)
                    }
                }
            }
            
            Item { Layout.fillHeight: true }
        }
    }
    
    // File dialogs
    FolderDialog {
        id: backupFolderDialog
        title: qsTr("Select Backup Folder")
        onAccepted: {
            backupSourceField.text = selectedFolder.toString().replace("file:///", "")
        }
    }
    
    FolderDialog {
        id: restoreFolderDialog
        title: qsTr("Select Restore Destination")
        onAccepted: {
            restoreDestinationField.text = selectedFolder.toString().replace("file:///", "")
        }
    }
}
