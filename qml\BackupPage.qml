import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs
import BackupPro 1.0

Page {
    id: backupPage
    
    property bool isDarkTheme: parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color accentColor: "#FF9800"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color successColor: "#4CAF50"
    readonly property color warningColor: "#FF9800"
    readonly property color errorColor: "#F44336"
    
    background: Rectangle {
        color: backgroundColor
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 24
        
        ColumnLayout {
            width: parent.width
            spacing: 24
            
            // Page header
            RowLayout {
                Layout.fillWidth: true
                
                Label {
                    text: qsTr("Backup")
                    font.pixelSize: 28
                    font.bold: true
                    color: textColor
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Quick Backup")
                    icon.source: "qrc:/icons/backup-quick.png"
                    enabled: !appCore.isBackupInProgress
                    
                    background: Rectangle {
                        color: parent.enabled ? primaryColor : borderColor
                        radius: 6
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                    
                    contentItem: RowLayout {
                        spacing: 8
                        
                        Image {
                            source: parent.parent.icon.source
                            Layout.preferredWidth: 16
                            Layout.preferredHeight: 16
                            fillMode: Image.PreserveAspectFit
                            
                            ColorOverlay {
                                anchors.fill: parent
                                source: parent
                                color: "white"
                            }
                        }
                        
                        Label {
                            text: parent.parent.text
                            color: "white"
                            font.pixelSize: 14
                        }
                    }
                    
                    onClicked: {
                        if (sourcePathField.text && destinationPathField.text) {
                            startBackup()
                        }
                    }
                }
            }
            
            // Main backup configuration
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Backup Configuration")
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 16
                    
                    // Source path selection
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8
                        
                        Label {
                            text: qsTr("Source Path")
                            color: textColor
                            font.pixelSize: 14
                            font.bold: true
                        }
                        
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 8
                            
                            TextField {
                                id: sourcePathField
                                Layout.fillWidth: true
                                placeholderText: qsTr("Select folder to backup...")
                                color: textColor
                                
                                background: Rectangle {
                                    color: backgroundColor
                                    border.color: parent.activeFocus ? primaryColor : borderColor
                                    border.width: parent.activeFocus ? 2 : 1
                                    radius: 4
                                    
                                    Behavior on border.color {
                                        ColorAnimation { duration: 150 }
                                    }
                                }
                            }
                            
                            Button {
                                text: qsTr("Browse")
                                onClicked: sourceFolderDialog.open()
                                
                                background: Rectangle {
                                    color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                    border.color: primaryColor
                                    border.width: 1
                                    radius: 4
                                }
                                
                                contentItem: Label {
                                    text: parent.text
                                    color: primaryColor
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                        }
                    }
                    
                    // Destination path selection
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8
                        
                        Label {
                            text: qsTr("Destination Path")
                            color: textColor
                            font.pixelSize: 14
                            font.bold: true
                        }
                        
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 8
                            
                            TextField {
                                id: destinationPathField
                                Layout.fillWidth: true
                                placeholderText: qsTr("Select backup destination...")
                                color: textColor
                                
                                background: Rectangle {
                                    color: backgroundColor
                                    border.color: parent.activeFocus ? primaryColor : borderColor
                                    border.width: parent.activeFocus ? 2 : 1
                                    radius: 4
                                    
                                    Behavior on border.color {
                                        ColorAnimation { duration: 150 }
                                    }
                                }
                            }
                            
                            Button {
                                text: qsTr("Browse")
                                onClicked: destinationFolderDialog.open()
                                
                                background: Rectangle {
                                    color: parent.hovered ? Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.1) : "transparent"
                                    border.color: primaryColor
                                    border.width: 1
                                    radius: 4
                                }
                                
                                contentItem: Label {
                                    text: parent.text
                                    color: primaryColor
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                            
                            DriveSelector {
                                id: driveSelector
                                onDriveSelected: function(drivePath) {
                                    destinationPathField.text = drivePath + "/BackupPro"
                                }
                            }
                        }
                    }
                    
                    // Backup options
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 24
                        
                        CheckBox {
                            id: encryptionCheckBox
                            text: qsTr("Enable Encryption")
                            checked: appCore.settings.encryptionEnabled
                            
                            indicator: Rectangle {
                                implicitWidth: 20
                                implicitHeight: 20
                                x: parent.leftPadding
                                y: parent.height / 2 - height / 2
                                radius: 3
                                border.color: parent.checked ? primaryColor : borderColor
                                border.width: parent.checked ? 2 : 1
                                color: parent.checked ? primaryColor : "transparent"
                                
                                Image {
                                    width: 12
                                    height: 12
                                    anchors.centerIn: parent
                                    source: "qrc:/icons/check.png"
                                    visible: parent.parent.checked
                                    
                                    ColorOverlay {
                                        anchors.fill: parent
                                        source: parent
                                        color: "white"
                                    }
                                }
                            }
                            
                            contentItem: Label {
                                text: parent.text
                                color: textColor
                                leftPadding: parent.indicator.width + parent.spacing
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                        
                        CheckBox {
                            id: incrementalCheckBox
                            text: qsTr("Incremental Backup")
                            checked: appCore.settings.incrementalBackupEnabled
                            
                            indicator: Rectangle {
                                implicitWidth: 20
                                implicitHeight: 20
                                x: parent.leftPadding
                                y: parent.height / 2 - height / 2
                                radius: 3
                                border.color: parent.checked ? primaryColor : borderColor
                                border.width: parent.checked ? 2 : 1
                                color: parent.checked ? primaryColor : "transparent"
                                
                                Image {
                                    width: 12
                                    height: 12
                                    anchors.centerIn: parent
                                    source: "qrc:/icons/check.png"
                                    visible: parent.parent.checked
                                    
                                    ColorOverlay {
                                        anchors.fill: parent
                                        source: parent
                                        color: "white"
                                    }
                                }
                            }
                            
                            contentItem: Label {
                                text: parent.text
                                color: textColor
                                leftPadding: parent.indicator.width + parent.spacing
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                        
                        Item { Layout.fillWidth: true }
                    }
                }
            }
            
            // Backup progress
            GroupBox {
                Layout.fillWidth: true
                title: qsTr("Backup Progress")
                visible: appCore.isBackupInProgress
                
                background: Rectangle {
                    color: surfaceColor
                    border.color: borderColor
                    border.width: 1
                    radius: 8
                }
                
                label: Label {
                    text: parent.title
                    color: textColor
                    font.pixelSize: 16
                    font.bold: true
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 8
                    bottomPadding: 8
                    
                    background: Rectangle {
                        color: surfaceColor
                        radius: 4
                    }
                }
                
                ProgressIndicator {
                    anchors.fill: parent
                    anchors.margins: 16
                    
                    progress: appCore.backupProgress
                    statusText: appCore.statusMessage
                    
                    onCancelRequested: appCore.stopBackup()
                }
            }
            
            // Action buttons
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                Button {
                    id: startBackupButton
                    text: appCore.isBackupInProgress ? qsTr("Stop Backup") : qsTr("Start Backup")
                    enabled: sourcePathField.text.length > 0 && destinationPathField.text.length > 0
                    Layout.preferredWidth: 150
                    
                    background: Rectangle {
                        color: {
                            if (!parent.enabled) return borderColor
                            if (appCore.isBackupInProgress) return errorColor
                            return parent.hovered ? Qt.darker(primaryColor, 1.1) : primaryColor
                        }
                        radius: 6
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        if (appCore.isBackupInProgress) {
                            appCore.stopBackup()
                        } else {
                            startBackup()
                        }
                    }
                }
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: qsTr("Schedule Backup")
                    enabled: !appCore.isBackupInProgress
                    
                    background: Rectangle {
                        color: parent.enabled ? (parent.hovered ? Qt.rgba(accentColor.r, accentColor.g, accentColor.b, 0.1) : "transparent") : "transparent"
                        border.color: parent.enabled ? accentColor : borderColor
                        border.width: 1
                        radius: 6
                    }
                    
                    contentItem: Label {
                        text: parent.text
                        color: parent.enabled ? accentColor : secondaryTextColor
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        // Navigate to schedule page with current settings
                        parent.parent.parent.parent.parent.stackView.push(parent.parent.parent.parent.parent.schedulePageComponent)
                    }
                }
            }
            
            Item { Layout.fillHeight: true }
        }
    }
    
    // File dialogs
    FolderDialog {
        id: sourceFolderDialog
        title: qsTr("Select Source Folder")
        onAccepted: {
            sourcePathField.text = selectedFolder.toString().replace("file:///", "")
        }
    }
    
    FolderDialog {
        id: destinationFolderDialog
        title: qsTr("Select Destination Folder")
        onAccepted: {
            destinationPathField.text = selectedFolder.toString().replace("file:///", "")
        }
    }
    
    // Functions
    function startBackup() {
        if (!sourcePathField.text || !destinationPathField.text) {
            return
        }
        
        appCore.startBackup(
            sourcePathField.text,
            destinationPathField.text,
            encryptionCheckBox.checked,
            incrementalCheckBox.checked
        )
    }
    
    // Initialize with default paths
    Component.onCompleted: {
        destinationPathField.text = appCore.settings.defaultBackupPath
    }
}
