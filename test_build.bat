@echo off
echo ========================================
echo BackupPro Build and Test Script
echo ========================================
echo.

REM Check if Qt is available
echo [1/6] Checking Qt installation...
qmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Qt/qmake not found in PATH
    echo Please install Qt and add it to your PATH
    pause
    exit /b 1
)
echo ✓ Qt found

REM Check if CMake is available
echo [2/6] Checking CMake installation...
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: CMake not found, will use QMake instead
    set USE_CMAKE=0
) else (
    echo ✓ CMake found
    set USE_CMAKE=1
)

REM Create build directory
echo [3/6] Setting up build directory...
if exist build rmdir /s /q build
mkdir build
cd build

REM Build the project
echo [4/6] Building BackupPro...
if %USE_CMAKE%==1 (
    echo Using CMake build system...
    cmake .. -G "MinGW Makefiles"
    if %errorlevel% neq 0 (
        echo ERROR: CMake configuration failed
        cd ..
        pause
        exit /b 1
    )
    cmake --build .
    if %errorlevel% neq 0 (
        echo ERROR: CMake build failed
        cd ..
        pause
        exit /b 1
    )
) else (
    echo Using QMake build system...
    qmake ../BackupPro.pro
    if %errorlevel% neq 0 (
        echo ERROR: QMake configuration failed
        cd ..
        pause
        exit /b 1
    )
    mingw32-make
    if %errorlevel% neq 0 (
        echo ERROR: Make build failed
        cd ..
        pause
        exit /b 1
    )
)

REM Check if executable was created
echo [5/6] Verifying build output...
if exist BackupPro.exe (
    echo ✓ BackupPro.exe created successfully
) else (
    echo ERROR: BackupPro.exe not found
    echo Build may have failed
    cd ..
    pause
    exit /b 1
)

REM Test run the application
echo [6/6] Testing application startup...
echo Starting BackupPro (will close automatically after 5 seconds)...
timeout /t 2 /nobreak >nul
start /wait /b BackupPro.exe &
timeout /t 5 /nobreak >nul
taskkill /f /im BackupPro.exe >nul 2>&1

cd ..

echo.
echo ========================================
echo Build and Test Complete!
echo ========================================
echo.
echo To run the application manually:
echo   cd build
echo   BackupPro.exe
echo.
echo Check TESTING_GUIDE.md for detailed testing instructions.
echo.
pause
