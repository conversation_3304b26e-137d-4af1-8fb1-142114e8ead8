cmake_minimum_required(VERSION 3.16)

project(BackupPro VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required Qt components
find_package(Qt6 REQUIRED COMPONENTS 
    Core 
    Gui 
    Quick 
    Qml 
    Widgets
    Network
    Concurrent
)

qt_standard_project_setup(REQUIRES 6.5)

# Include directories
include_directories(include)

# Source files
set(SOURCES
    src/main.cpp
    src/appcore.cpp
    src/backupmanager.cpp
    src/drivewatcher.cpp
    src/fileoperations.cpp
    src/scheduler.cpp
    src/settings.cpp
    src/encryption.cpp
)

# Header files
set(HEADERS
    include/appcore.h
    include/backupmanager.h
    include/drivewatcher.h
    include/fileoperations.h
    include/scheduler.h
    include/settings.h
    include/encryption.h
)

# Create executable
qt_add_executable(BackupPro
    ${SOURCES}
    ${HEADERS}
)

# Set target properties
set_target_properties(BackupPro PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Link Qt libraries
target_link_libraries(BackupPro PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Quick
    Qt6::Qml
    Qt6::Widgets
    Qt6::Network
    Qt6::Concurrent
)

# Add QML module
qt_add_qml_module(BackupPro
    URI BackupPro
    VERSION 1.0
    QML_FILES
        resources/qml/main.qml
        resources/qml/BackupPage.qml
        resources/qml/RestorePage.qml
        resources/qml/SchedulePage.qml
        resources/qml/HistoryPage.qml
        resources/qml/SettingsPage.qml
        resources/qml/components/NavButton.qml
        resources/qml/components/DriveSelector.qml
        resources/qml/components/ProgressIndicator.qml
        resources/qml/components/BackupItemDelegate.qml
    RESOURCES
        resources/icons/app-icon.png
        resources/icons/material-icons/backup.svg
        resources/icons/material-icons/restore.svg
        resources/icons/material-icons/schedule.svg
        resources/icons/material-icons/history.svg
        resources/icons/material-icons/settings.svg
        resources/icons/material-icons/usb.svg
        resources/icons/material-icons/folder.svg
        resources/themes/dark-theme.json
        resources/themes/light-theme.json
    DEPENDENCIES QtQuick
)

# Installation
install(TARGETS BackupPro
    BUNDLE DESTINATION .
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

# Generate deployment script
qt_generate_deploy_qml_app_script(
    TARGET BackupPro
    OUTPUT_SCRIPT deploy_script
    MACOS_BUNDLE_POST_BUILD
    NO_UNSUPPORTED_PLATFORM_ERROR
    DEPLOY_USER_QML_MODULES_ON_UNSUPPORTED_PLATFORM
)
install(SCRIPT ${deploy_script})

# Enable testing
enable_testing()
add_subdirectory(tests)
