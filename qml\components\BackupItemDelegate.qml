import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ItemDelegate {
    id: backupItemDelegate
    
    property string itemPath: ""
    property string itemType: "file" // "file" or "folder"
    property string itemSize: ""
    property bool isSelected: false
    
    signal removeRequested()
    
    property bool isDarkTheme: parent.parent.parent.parent.isDarkTheme
    
    // Colors
    readonly property color primaryColor: "#2196F3"
    readonly property color backgroundColor: isDarkTheme ? "#1e1e1e" : "#f5f5f5"
    readonly property color surfaceColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    readonly property color textColor: isDarkTheme ? "#ffffff" : "#000000"
    readonly property color secondaryTextColor: isDarkTheme ? "#b0b0b0" : "#666666"
    readonly property color borderColor: isDarkTheme ? "#404040" : "#e0e0e0"
    readonly property color hoverColor: isDarkTheme ? "#3d3d3d" : "#f0f0f0"
    readonly property color errorColor: "#F44336"
    
    width: parent.width
    height: 60
    
    background: Rectangle {
        color: backupItemDelegate.hovered ? hoverColor : "transparent"
        border.color: backupItemDelegate.isSelected ? primaryColor : borderColor
        border.width: backupItemDelegate.isSelected ? 2 : 1
        radius: 6
        
        Behavior on color {
            ColorAnimation { duration: 150 }
        }
        
        Behavior on border.color {
            ColorAnimation { duration: 150 }
        }
    }
    
    contentItem: RowLayout {
        anchors.fill: parent
        anchors.margins: 12
        spacing: 12
        
        // Selection checkbox
        CheckBox {
            id: selectionCheckBox
            checked: backupItemDelegate.isSelected
            
            indicator: Rectangle {
                implicitWidth: 18
                implicitHeight: 18
                x: parent.leftPadding
                y: parent.height / 2 - height / 2
                radius: 3
                border.color: parent.checked ? primaryColor : borderColor
                border.width: parent.checked ? 2 : 1
                color: parent.checked ? primaryColor : "transparent"
                
                Image {
                    width: 10
                    height: 10
                    anchors.centerIn: parent
                    source: "qrc:/icons/check.png"
                    visible: parent.parent.checked
                    
                    ColorOverlay {
                        anchors.fill: parent
                        source: parent
                        color: "white"
                    }
                }
            }
            
            onCheckedChanged: {
                backupItemDelegate.isSelected = checked
            }
        }
        
        // File/folder icon
        Image {
            id: itemIcon
            Layout.preferredWidth: 24
            Layout.preferredHeight: 24
            source: backupItemDelegate.itemType === "folder" ? "qrc:/icons/folder.png" : getFileIcon()
            fillMode: Image.PreserveAspectFit
            
            ColorOverlay {
                anchors.fill: parent
                source: parent
                color: backupItemDelegate.itemType === "folder" ? "#FFA726" : textColor
            }
            
            function getFileIcon() {
                var extension = backupItemDelegate.itemPath.split('.').pop().toLowerCase()
                switch (extension) {
                    case 'txt':
                    case 'doc':
                    case 'docx':
                    case 'pdf':
                        return "qrc:/icons/document.png"
                    case 'jpg':
                    case 'jpeg':
                    case 'png':
                    case 'gif':
                    case 'bmp':
                        return "qrc:/icons/image.png"
                    case 'mp3':
                    case 'wav':
                    case 'flac':
                        return "qrc:/icons/audio.png"
                    case 'mp4':
                    case 'avi':
                    case 'mkv':
                        return "qrc:/icons/video.png"
                    default:
                        return "qrc:/icons/file.png"
                }
            }
        }
        
        // Item information
        ColumnLayout {
            Layout.fillWidth: true
            spacing: 2
            
            Label {
                text: getItemName()
                color: textColor
                font.pixelSize: 14
                font.bold: backupItemDelegate.itemType === "folder"
                elide: Text.ElideRight
                Layout.fillWidth: true
                
                function getItemName() {
                    var parts = backupItemDelegate.itemPath.split('/')
                    if (parts.length === 0) {
                        parts = backupItemDelegate.itemPath.split('\\')
                    }
                    return parts[parts.length - 1] || backupItemDelegate.itemPath
                }
            }
            
            RowLayout {
                spacing: 16
                
                Label {
                    text: backupItemDelegate.itemPath
                    color: secondaryTextColor
                    font.pixelSize: 11
                    elide: Text.ElideMiddle
                    Layout.fillWidth: true
                }
                
                Label {
                    text: backupItemDelegate.itemSize
                    color: secondaryTextColor
                    font.pixelSize: 11
                    visible: backupItemDelegate.itemSize.length > 0
                }
                
                Label {
                    text: backupItemDelegate.itemType === "folder" ? qsTr("Folder") : qsTr("File")
                    color: secondaryTextColor
                    font.pixelSize: 11
                    font.italic: true
                }
            }
        }
        
        // Action buttons
        RowLayout {
            spacing: 4
            
            Button {
                id: removeButton
                width: 32
                height: 32
                
                background: Rectangle {
                    color: parent.hovered ? Qt.rgba(errorColor.r, errorColor.g, errorColor.b, 0.1) : "transparent"
                    radius: 16
                    
                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }
                }
                
                contentItem: Image {
                    source: "qrc:/icons/delete.png"
                    width: 16
                    height: 16
                    fillMode: Image.PreserveAspectFit
                    
                    ColorOverlay {
                        anchors.fill: parent
                        source: parent
                        color: parent.parent.hovered ? errorColor : secondaryTextColor
                        
                        Behavior on color {
                            ColorAnimation { duration: 150 }
                        }
                    }
                }
                
                onClicked: backupItemDelegate.removeRequested()
                
                ToolTip.visible: hovered
                ToolTip.text: qsTr("Remove from backup list")
                ToolTip.delay: 1000
            }
        }
    }
    
    // Hover effect
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.NoButton
        
        onEntered: backupItemDelegate.hovered = true
        onExited: backupItemDelegate.hovered = false
    }
    
    // Selection on click
    onClicked: {
        backupItemDelegate.isSelected = !backupItemDelegate.isSelected
        selectionCheckBox.checked = backupItemDelegate.isSelected
    }
}
